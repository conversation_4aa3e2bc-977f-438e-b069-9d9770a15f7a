import{s as ue,_ as Ze,M as el,g as ll,r as c,A as al,R as tl,v as we,d as y,O as Ve,c as I,o as d,P as V,e as l,Q as ol,k as o,f as t,l as Ce,J as N,K as U,i as p,j as oe,q as v,N as O,h as z,a4 as J}from"./index-B3e47hUR.js";import{l as re,b as rl,g as Ie,d as ke,u as Ne,c as Ue}from"./warehouse-I5QKWbAT.js";import{o as ul}from"./operator-b9e2JnZC.js";import{C as We}from"./index-DFSldapw.js";function nl(){return ue({url:"/system/area/provinces",method:"get"})}function Pe(A){return ue({url:"/system/area/cities/"+A,method:"get"})}function xe(A){return ue({url:"/system/area/districts/"+A,method:"get"})}const sl={class:"app-container"},dl={class:"dialog-footer"},il={class:"child-warehouse-management"},pl={class:"mb-4"},ml={class:"dialog-footer"},fl=el({name:"Warehouse"}),cl=Object.assign(fl,{components:{CustomPagination:We}},{setup(A){const{proxy:m}=ll(),{warehouse_status:S,parking_lot_status:ne}=m.useDict("warehouse_status","parking_lot_status"),se=c([]),q=c([]),B=c([]),Se=c([]),de=c([]),D=c([]),$=c([]),P=c(!1),E=c(!0),j=c(!0),L=c([]),ie=c(!0),pe=c(!0),me=c(0),K=c(""),Q=c(!1),x=c(!1),R=c(!1),M=c([]),G=c(""),T=c({}),fe=c(!1),$e=al({form:{},queryParams:{pageNum:1,pageSize:10,projectName:null,warehouseName:null,operatorId:null,status:null},rules:{warehouseName:[{required:!0,message:"场库名称不能为空",trigger:"blur"}],operatorId:[{required:!0,message:"所属运营商不能为空",trigger:"change"}],warehouseType:[{required:!0,message:"类型不能为空",trigger:"change"}],parentId:[{validator:(n,e,u)=>{r.value.warehouseType==="child"&&(!e||e===0)?u(new Error("选择子场库时，所属场库不能为空")):u()},trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}],totalParking:[{required:!0,message:"总停车位数不能为空",trigger:"blur"},{type:"number",min:1,message:"总停车位数必须大于0",trigger:"blur"}],longitude:[{pattern:/^-?((1[0-7]\d)|(\d{1,2}))(\.\d+)?$|^-?180(\.0+)?$/,message:"经度范围应在-180到180之间",trigger:"blur"}],latitude:[{pattern:/^-?([1-8]?\d(\.\d+)?|90(\.0+)?)$/,message:"纬度范围应在-90到90之间",trigger:"blur"}]},childWarehouseForm:{},childWarehouseRules:{warehouseName:[{required:!0,message:"子场库名称不能为空",trigger:"blur"}],operatorId:[{required:!0,message:"所属运营商不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}),{queryParams:g,form:r,rules:Te,childWarehouseForm:h,childWarehouseRules:Oe}=tl($e),H=we(()=>r.value.warehouseType==="main"?"主场库":"子场库"),X=we({get(){if(!r.value.carouselImages)return"";try{if(typeof r.value.carouselImages=="string"&&r.value.carouselImages.startsWith("[")){let n=JSON.parse(r.value.carouselImages);if(Array.isArray(n)&&n.length>0&&typeof n[0]=="string"&&n[0].startsWith("[")&&n[0].endsWith("]"))try{n=JSON.parse(n[0])}catch(u){console.warn("解析内层JSON失败，使用原始数据:",u)}return Array.isArray(n)||(n=[n]),n.filter(u=>u&&typeof u=="string"&&u.trim()&&u!=="undefined"&&u!=="null"&&!u.includes("[")&&!u.includes("]")).join(",")}return r.value.carouselImages||""}catch(n){return console.error("轮播图数据格式转换失败:",n),""}},set(n){r.value.carouselImages=n||null}});function F(){g.value.pageNum=1,k()}function De(){m.resetForm("queryRef"),F()}function k(){E.value=!0;const n={...g.value,parentId:0};re(n).then(e=>{se.value=e.rows||[],me.value=e.total||0,E.value=!1}).catch(e=>{console.error("获取场库列表失败:",e),E.value=!1,J(e)||m.$modal.msgError("获取数据失败，请刷新页面重试")})}function Ae(){rl({}).then(n=>{Se.value=n.data.filter(e=>e.parentId===0)})}function qe(){ul().then(n=>{q.value=n.data})}function Ee(){nl().then(n=>{de.value=n.data}).catch(n=>{console.error("获取省份列表失败:",n)})}function je(n){r.value.cityCode=null,r.value.areaCode=null,D.value=[],$.value=[],n&&Pe(n).then(e=>{D.value=e.data}).catch(e=>{console.error("获取城市列表失败:",e)})}function Re(n){r.value.areaCode=null,$.value=[],n&&xe(n).then(e=>{$.value=e.data}).catch(e=>{console.error("获取区县列表失败:",e)})}function ce(){re({parentId:0,status:1}).then(n=>{B.value=n.rows||[]}).catch(n=>{console.error("获取父级场库列表失败:",n)})}function Fe(){P.value=!1,Y()}function Y(){r.value={id:null,projectName:null,warehouseName:null,operatorId:null,warehouseType:"main",parentId:0,smartsType:0,totalParking:0,provinceCode:null,cityCode:null,areaCode:null,address:null,longitude:null,latitude:null,carouselImages:null,status:1,remark:null,leasePropertyNo:null,leaseAddress:null,leaseDetailAddress:null},D.value=[],$.value=[],B.value=[],fe.value=!1,m.resetForm("warehouseRef")}function ze(n){L.value=n.map(e=>e.id),ie.value=n.length!=1,pe.value=!n.length}function Je(){Y(),P.value=!0,K.value="添加场库信息"}function ge(n){Y();const e=n.id||L.value;Ie(e).then(u=>{if(!u.data){console.error("获取场库详情失败: 返回数据为空"),m.$modal.msgError("获取场库信息失败，数据为空");return}r.value=u.data,r.value.warehouseType=r.value.parentId==0||r.value.parentId===null||r.value.parentId===void 0?"main":"child",r.value.parentId===0&&(fe.value=!1),r.value.warehouseType==="child"&&ce(),r.value&&r.value.provinceCode&&Pe(r.value.provinceCode).then(s=>{D.value=s.data,r.value.cityCode&&xe(r.value.cityCode).then(f=>{$.value=f.data})}),P.value=!0,K.value="修改场库信息"}).catch(u=>{console.error("获取场库详情失败:",u),J(u)||m.$modal.msgError("获取场库信息失败，请稍后重试")})}function Be(){m.$refs.warehouseRef.validate(n=>{if(n){const e={...r.value};if(e.warehouseType==="main")e.parentId=0;else if(e.warehouseType==="child"&&(!e.parentId||e.parentId===0)){m.$modal.msgError("选择子场库时，必须选择所属场库");return}delete e.warehouseType;try{const u=m.$store.state.user;u&&u.id&&(r.value.id||(e.createBy=u.id),e.updateBy=u.id)}catch(u){console.warn("获取用户ID失败:",u)}if(e.carouselImages)try{let u=!1;try{const s=JSON.parse(e.carouselImages);Array.isArray(s)&&(u=!0)}catch{}if(!u)if(typeof e.carouselImages=="string"&&e.carouselImages.includes(",")){const s=e.carouselImages.split(",").map(f=>f.trim()).filter(f=>f&&f!=="undefined"&&f!=="null");e.carouselImages=s.length>0?JSON.stringify(s):null}else if(typeof e.carouselImages=="string"&&e.carouselImages.trim()){const s=e.carouselImages.trim();s!=="undefined"&&s!=="null"?e.carouselImages=JSON.stringify([s]):e.carouselImages=null}else(!e.carouselImages||e.carouselImages.trim()==="")&&(e.carouselImages=null)}catch(u){console.error("轮播图数据格式转换失败:",u),e.carouselImages=null}else e.carouselImages=null;r.value.id!=null?Ne(e).then(u=>{m.$modal.msgSuccess("修改成功"),P.value=!1,k()}).catch(u=>{console.error("修改场库失败:",u),J(u)||m.$modal.msgError("修改失败，请稍后重试")}):Ue(e).then(u=>{m.$modal.msgSuccess("新增成功"),P.value=!1,k()}).catch(u=>{console.error("新增场库失败:",u),J(u)||m.$modal.msgError("新增失败，请稍后重试")})}})}function he(n){const e=n.id||L.value;m.$modal.confirm('是否确认删除场库信息编号为"'+e+'"的数据项？').then(function(){return ke(e)}).then(()=>{k(),m.$modal.msgSuccess("删除成功")}).catch(()=>{})}function Le(){m.download("system/platform/warehouse/export",{...g.value},`warehouse_${new Date().getTime()}.xlsx`)}function Z(n){R.value=!0,re({parentId:n,pageNum:1,pageSize:1e3}).then(u=>{M.value=u.rows||[],R.value=!1}).catch(()=>{M.value=[],R.value=!1})}function ee(){h.value={id:null,warehouseName:null,parentId:T.value.id,operatorId:T.value.operatorId,totalParking:0,status:1,remark:null},m.resetForm("childWarehouseRef")}function Ke(){ee(),x.value=!0,G.value="新增子场库"}function Qe(n){ee();const e=n.id;Ie(e).then(u=>{h.value=u.data,x.value=!0,G.value="修改子场库"})}function Me(){m.$refs.childWarehouseRef.validate(n=>{n&&(h.value.id!=null?Ne(h.value).then(e=>{m.$modal.msgSuccess("修改成功"),x.value=!1,Z(T.value.id),k()}):Ue(h.value).then(e=>{m.$modal.msgSuccess("新增成功"),x.value=!1,Z(T.value.id),k()}))})}function Ge(){x.value=!1,ee()}function He(n){const e=n.id;m.$modal.confirm(`是否确认删除子场库"${n.warehouseName}"？`).then(function(){return ke(e)}).then(()=>{Z(T.value.id),k(),m.$modal.msgSuccess("删除成功")}).catch(()=>{})}return k(),qe(),Ee(),ce(),Ae(),(n,e)=>{const u=y("el-input"),s=y("el-form-item"),f=y("el-option"),C=y("el-select"),_=y("el-button"),le=y("el-form"),i=y("el-col"),Xe=y("right-toolbar"),b=y("el-row"),w=y("el-table-column"),ve=y("dict-tag"),_e=y("el-table"),ye=y("el-input-number"),Ye=y("image-upload"),ae=y("el-dialog"),W=Ve("hasPermi"),be=Ve("loading");return d(),I("div",sl,[V(l(le,{model:o(g),ref:"queryRef",inline:!0,"label-width":"68px"},{default:t(()=>[l(s,{label:"项目名称",prop:"projectName"},{default:t(()=>[l(u,{modelValue:o(g).projectName,"onUpdate:modelValue":e[0]||(e[0]=a=>o(g).projectName=a),placeholder:"请输入项目名称",clearable:"",style:{width:"200px"},onKeyup:Ce(F,["enter"])},null,8,["modelValue"])]),_:1}),l(s,{label:"运营商",prop:"operatorId"},{default:t(()=>[l(C,{modelValue:o(g).operatorId,"onUpdate:modelValue":e[1]||(e[1]=a=>o(g).operatorId=a),placeholder:"请选择运营商",clearable:"",style:{width:"200px"}},{default:t(()=>[(d(!0),I(N,null,U(o(q),a=>(d(),p(f,{key:a.id,label:a.companyName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"场库名称",prop:"warehouseName"},{default:t(()=>[l(u,{modelValue:o(g).warehouseName,"onUpdate:modelValue":e[2]||(e[2]=a=>o(g).warehouseName=a),placeholder:"请输入场库名称",clearable:"",style:{width:"200px"},onKeyup:Ce(F,["enter"])},null,8,["modelValue"])]),_:1}),l(s,{label:"状态",prop:"status"},{default:t(()=>[l(C,{modelValue:o(g).status,"onUpdate:modelValue":e[3]||(e[3]=a=>o(g).status=a),placeholder:"请选择状态",clearable:"",style:{width:"200px"}},{default:t(()=>[o(S)&&o(S).length>0?(d(!0),I(N,{key:0},U(o(S),a=>(d(),p(f,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128)):oe("",!0),!o(S)||o(S).length===0?(d(),p(f,{key:1,disabled:"",value:""},{default:t(()=>[v("字典数据加载中...")]),_:1})):oe("",!0)]),_:1},8,["modelValue"])]),_:1}),l(s,null,{default:t(()=>[l(_,{type:"primary",icon:"Search",onClick:F},{default:t(()=>[v("搜索")]),_:1}),l(_,{icon:"Refresh",onClick:De},{default:t(()=>[v("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ol,o(j)]]),l(b,{gutter:10,class:"mb8"},{default:t(()=>[l(i,{span:1.5},{default:t(()=>[V((d(),p(_,{type:"primary",plain:"",icon:"Plus",onClick:Je},{default:t(()=>[v("新增")]),_:1})),[[W,["platform:warehouse:add"]]])]),_:1}),l(i,{span:1.5},{default:t(()=>[V((d(),p(_,{type:"success",plain:"",icon:"Edit",disabled:o(ie),onClick:ge},{default:t(()=>[v("修改")]),_:1},8,["disabled"])),[[W,["platform:warehouse:edit"]]])]),_:1}),l(i,{span:1.5},{default:t(()=>[V((d(),p(_,{type:"danger",plain:"",icon:"Delete",disabled:o(pe),onClick:he},{default:t(()=>[v("删除")]),_:1},8,["disabled"])),[[W,["platform:warehouse:remove"]]])]),_:1}),l(i,{span:1.5},{default:t(()=>[V((d(),p(_,{type:"warning",plain:"",icon:"Download",onClick:Le},{default:t(()=>[v("导出")]),_:1})),[[W,["platform:warehouse:export"]]])]),_:1}),l(Xe,{showSearch:o(j),"onUpdate:showSearch":e[4]||(e[4]=a=>O(j)?j.value=a:null),onQueryTable:k},null,8,["showSearch"])]),_:1}),V((d(),p(_e,{data:o(se),onSelectionChange:ze},{default:t(()=>[l(w,{type:"selection",width:"55",align:"center"}),l(w,{label:"项目名称",align:"center",prop:"projectName","show-overflow-tooltip":!0}),l(w,{label:"运营商",align:"center",prop:"operatorName","show-overflow-tooltip":!0,width:"300"}),l(w,{label:"场库名称",align:"center",prop:"warehouseName","show-overflow-tooltip":!0}),l(w,{label:"详细地址",align:"center",prop:"address","show-overflow-tooltip":!0,width:"300"}),l(w,{label:"总车位",align:"center",prop:"totalParking"}),l(w,{label:"状态",align:"center",prop:"status",width:"120"},{default:t(a=>[l(ve,{options:o(S),value:a.row.status},null,8,["options","value"])]),_:1}),l(w,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"200"},{default:t(a=>[V((d(),p(_,{link:"",type:"primary",icon:"Edit",onClick:te=>ge(a.row)},{default:t(()=>[v("修改")]),_:2},1032,["onClick"])),[[W,["platform:warehouse:edit"]]]),V((d(),p(_,{link:"",type:"danger",icon:"Delete",onClick:te=>he(a.row)},{default:t(()=>[v("删除")]),_:2},1032,["onClick"])),[[W,["platform:warehouse:remove"]]])]),_:1})]),_:1},8,["data"])),[[be,o(E)]]),l(We,{total:o(me),"current-page":o(g).pageNum,"onUpdate:currentPage":e[5]||(e[5]=a=>o(g).pageNum=a),"page-size":o(g).pageSize,"onUpdate:pageSize":e[6]||(e[6]=a=>o(g).pageSize=a),onPagination:k},null,8,["total","current-page","page-size"]),l(ae,{title:o(K),modelValue:o(P),"onUpdate:modelValue":e[26]||(e[26]=a=>O(P)?P.value=a:null),width:"1000px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:t(()=>[z("div",dl,[l(_,{type:"primary",onClick:Be},{default:t(()=>[v("确 定")]),_:1}),l(_,{onClick:Fe},{default:t(()=>[v("取 消")]),_:1})])]),default:t(()=>[l(le,{ref:"warehouseRef",model:o(r),rules:o(Te),"label-width":"100px"},{default:t(()=>[l(b,null,{default:t(()=>[l(i,{span:12},{default:t(()=>[l(s,{label:"场库名称",prop:"warehouseName"},{default:t(()=>[l(u,{modelValue:o(r).warehouseName,"onUpdate:modelValue":e[7]||(e[7]=a=>o(r).warehouseName=a),placeholder:"请输入场库名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:12},{default:t(()=>[l(s,{label:"类型",prop:"warehouseType"},{default:t(()=>[l(u,{modelValue:o(H),"onUpdate:modelValue":e[8]||(e[8]=a=>O(H)?H.value=a:null),readonly:"",placeholder:"类型"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b,null,{default:t(()=>[l(i,{span:12},{default:t(()=>[l(s,{label:"所属运营商",prop:"operatorId"},{default:t(()=>[l(C,{modelValue:o(r).operatorId,"onUpdate:modelValue":e[9]||(e[9]=a=>o(r).operatorId=a),placeholder:"请选择运营商",style:{width:"100%"},"popper-class":"operator-select-dropdown"},{default:t(()=>[(d(!0),I(N,null,U(o(q),a=>(d(),p(f,{key:a.id,label:a.companyName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(i,{span:12},{default:t(()=>[l(s,{label:"状态",prop:"status"},{default:t(()=>[l(C,{modelValue:o(r).status,"onUpdate:modelValue":e[10]||(e[10]=a=>o(r).status=a),placeholder:"请选择状态"},{default:t(()=>[(d(!0),I(N,null,U(o(S),a=>(d(),p(f,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(r).warehouseType==="child"?(d(),p(b,{key:0},{default:t(()=>[l(i,{span:12},{default:t(()=>[l(s,{label:"所属场库",prop:"parentId"},{default:t(()=>[l(C,{modelValue:o(r).parentId,"onUpdate:modelValue":e[11]||(e[11]=a=>o(r).parentId=a),placeholder:"请选择所属场库",style:{width:"100%"},clearable:""},{default:t(()=>[(d(!0),I(N,null,U(o(B),a=>(d(),p(f,{key:a.id,label:a.warehouseName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):oe("",!0),l(b,null,{default:t(()=>[l(i,{span:12},{default:t(()=>[l(s,{label:"项目名称",prop:"projectName"},{default:t(()=>[l(u,{modelValue:o(r).projectName,"onUpdate:modelValue":e[12]||(e[12]=a=>o(r).projectName=a),placeholder:"请输入项目名称"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:12},{default:t(()=>[l(s,{label:"智能类型",prop:"smartsType"},{default:t(()=>[l(C,{modelValue:o(r).smartsType,"onUpdate:modelValue":e[13]||(e[13]=a=>o(r).smartsType=a),placeholder:"请选择智能类型"},{default:t(()=>[l(f,{label:"普通停车场",value:0}),l(f,{label:"智能停车场",value:1}),l(f,{label:"无人值守",value:2})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b,null,{default:t(()=>[l(i,{span:8},{default:t(()=>[l(s,{label:"总停车位",prop:"totalParking"},{default:t(()=>[l(ye,{modelValue:o(r).totalParking,"onUpdate:modelValue":e[14]||(e[14]=a=>o(r).totalParking=a),min:0,placeholder:"总停车位数"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b,null,{default:t(()=>[l(i,{span:8},{default:t(()=>[l(s,{label:"省份",prop:"provinceCode"},{default:t(()=>[l(C,{modelValue:o(r).provinceCode,"onUpdate:modelValue":e[15]||(e[15]=a=>o(r).provinceCode=a),placeholder:"请选择省份",clearable:"",onChange:je},{default:t(()=>[(d(!0),I(N,null,U(o(de),a=>(d(),p(f,{key:a.areaCode,label:a.areaName,value:a.areaCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(i,{span:8},{default:t(()=>[l(s,{label:"城市",prop:"cityCode"},{default:t(()=>[l(C,{modelValue:o(r).cityCode,"onUpdate:modelValue":e[16]||(e[16]=a=>o(r).cityCode=a),placeholder:"请选择城市",clearable:"",onChange:Re,disabled:!o(r).provinceCode},{default:t(()=>[(d(!0),I(N,null,U(o(D),a=>(d(),p(f,{key:a.areaCode,label:a.areaName,value:a.areaCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(i,{span:8},{default:t(()=>[l(s,{label:"区县",prop:"areaCode"},{default:t(()=>[l(C,{modelValue:o(r).areaCode,"onUpdate:modelValue":e[17]||(e[17]=a=>o(r).areaCode=a),placeholder:"请选择区县",clearable:"",disabled:!o(r).cityCode},{default:t(()=>[(d(!0),I(N,null,U(o($),a=>(d(),p(f,{key:a.areaCode,label:a.areaName,value:a.areaCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(s,{label:"详细地址",prop:"address"},{default:t(()=>[l(u,{modelValue:o(r).address,"onUpdate:modelValue":e[18]||(e[18]=a=>o(r).address=a),placeholder:"请输入详细地址"},null,8,["modelValue"])]),_:1}),l(b,null,{default:t(()=>[l(i,{span:8},{default:t(()=>[l(s,{label:"租赁物业编号",prop:"leasePropertyNo"},{default:t(()=>[l(u,{modelValue:o(r).leasePropertyNo,"onUpdate:modelValue":e[19]||(e[19]=a=>o(r).leasePropertyNo=a),placeholder:"请输入租赁物业编号"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:8},{default:t(()=>[l(s,{label:"租赁地址",prop:"leaseAddress"},{default:t(()=>[l(u,{modelValue:o(r).leaseAddress,"onUpdate:modelValue":e[20]||(e[20]=a=>o(r).leaseAddress=a),placeholder:"请输入租赁地址"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:8},{default:t(()=>[l(s,{label:"租赁详细地址",prop:"leaseDetailAddress"},{default:t(()=>[l(u,{modelValue:o(r).leaseDetailAddress,"onUpdate:modelValue":e[21]||(e[21]=a=>o(r).leaseDetailAddress=a),placeholder:"请输入租赁详细地址"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b,null,{default:t(()=>[l(i,{span:12},{default:t(()=>[l(s,{label:"经度",prop:"longitude"},{default:t(()=>[l(u,{modelValue:o(r).longitude,"onUpdate:modelValue":e[22]||(e[22]=a=>o(r).longitude=a),placeholder:"请输入经度（-180到180）"},null,8,["modelValue"])]),_:1})]),_:1}),l(i,{span:12},{default:t(()=>[l(s,{label:"纬度",prop:"latitude"},{default:t(()=>[l(u,{modelValue:o(r).latitude,"onUpdate:modelValue":e[23]||(e[23]=a=>o(r).latitude=a),placeholder:"请输入纬度（-90到90）"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b),l(s,{label:"轮播图片",prop:"carouselImages"},{default:t(()=>[l(Ye,{modelValue:o(X),"onUpdate:modelValue":e[24]||(e[24]=a=>O(X)?X.value=a:null),limit:6,action:"/file/upload/path",data:{path:"warehouse"},"file-size":5,"file-type":["png","jpg","jpeg","gif","webp"]},null,8,["modelValue"])]),_:1}),l(s,{label:"备注",prop:"remark"},{default:t(()=>[l(u,{modelValue:o(r).remark,"onUpdate:modelValue":e[25]||(e[25]=a=>o(r).remark=a),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),l(ae,{title:`${o(T).warehouseName} - 子场库管理`,modelValue:o(Q),"onUpdate:modelValue":e[27]||(e[27]=a=>O(Q)?Q.value=a:null),width:"1200px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{default:t(()=>[z("div",il,[z("div",pl,[V((d(),p(_,{type:"primary",icon:"Plus",onClick:Ke},{default:t(()=>[v(" 新增子场库 ")]),_:1})),[[W,["platform:warehouse:add"]]])]),V((d(),p(_e,{data:o(M),border:""},{default:t(()=>[l(w,{label:"子场库名称",align:"center",prop:"warehouseName","show-overflow-tooltip":!0}),l(w,{label:"总车位",align:"center",prop:"totalParking"}),l(w,{label:"状态",align:"center",prop:"status",width:"100"},{default:t(a=>[l(ve,{options:o(ne),value:a.row.status},null,8,["options","value"])]),_:1}),l(w,{label:"操作",align:"center",width:"150"},{default:t(a=>[V((d(),p(_,{link:"",type:"primary",icon:"Edit",onClick:te=>Qe(a.row)},{default:t(()=>[v(" 修改 ")]),_:2},1032,["onClick"])),[[W,["platform:warehouse:edit"]]]),V((d(),p(_,{link:"",type:"danger",icon:"Delete",onClick:te=>He(a.row)},{default:t(()=>[v(" 删除 ")]),_:2},1032,["onClick"])),[[W,["platform:warehouse:remove"]]])]),_:1})]),_:1},8,["data"])),[[be,o(R)]])])]),_:1},8,["title","modelValue"]),l(ae,{title:o(G),modelValue:o(x),"onUpdate:modelValue":e[33]||(e[33]=a=>O(x)?x.value=a:null),width:"1000px","append-to-body":""},{footer:t(()=>[z("div",ml,[l(_,{type:"primary",onClick:Me},{default:t(()=>[v("确 定")]),_:1}),l(_,{onClick:Ge},{default:t(()=>[v("取 消")]),_:1})])]),default:t(()=>[l(le,{ref:"childWarehouseRef",model:o(h),rules:o(Oe),"label-width":"120px"},{default:t(()=>[l(b,null,{default:t(()=>[l(i,{span:12},{default:t(()=>[l(s,{label:"子场库名称",prop:"warehouseName"},{default:t(()=>[l(u,{modelValue:o(h).warehouseName,"onUpdate:modelValue":e[28]||(e[28]=a=>o(h).warehouseName=a),placeholder:"请输入子场库名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b,null,{default:t(()=>[l(i,{span:12},{default:t(()=>[l(s,{label:"所属运营商",prop:"operatorId"},{default:t(()=>[l(C,{modelValue:o(h).operatorId,"onUpdate:modelValue":e[29]||(e[29]=a=>o(h).operatorId=a),placeholder:"请选择运营商",style:{width:"100%"}},{default:t(()=>[(d(!0),I(N,null,U(o(q),a=>(d(),p(f,{key:a.id,label:a.companyName,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b,null,{default:t(()=>[l(i,{span:8},{default:t(()=>[l(s,{label:"总车位数",prop:"totalParking"},{default:t(()=>[l(ye,{modelValue:o(h).totalParking,"onUpdate:modelValue":e[30]||(e[30]=a=>o(h).totalParking=a),min:0,placeholder:"总车位数",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(b,null,{default:t(()=>[l(i,{span:12},{default:t(()=>[l(s,{label:"状态",prop:"status"},{default:t(()=>[l(C,{modelValue:o(h).status,"onUpdate:modelValue":e[31]||(e[31]=a=>o(h).status=a),placeholder:"请选择状态"},{default:t(()=>[(d(!0),I(N,null,U(o(ne),a=>(d(),p(f,{key:a.value,label:a.label,value:parseInt(a.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(s,{label:"备注",prop:"remark"},{default:t(()=>[l(u,{modelValue:o(h).remark,"onUpdate:modelValue":e[32]||(e[32]=a=>o(h).remark=a),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),yl=Ze(cl,[["__scopeId","data-v-ad5f1d7a"]]);export{yl as default};
