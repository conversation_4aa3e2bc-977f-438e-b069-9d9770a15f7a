<template>
  <view class="agreement-container">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="iconfont icon-back"></text>
      </view>
      <view class="nav-title">隐私政策</view>
      <view class="nav-placeholder"></view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content-wrapper">
      <view v-if="loading" class="loading-container">
        <text class="loading-text">加载中...</text>
      </view>
      
      <view v-else-if="error" class="error-container">
        <text class="error-text">{{ error }}</text>
        <button class="retry-btn" @click="loadAgreement">重新加载</button>
      </view>
      
      <view v-else class="agreement-content">
        <view class="agreement-title">{{ agreementData.agreementTitle || '隐私政策' }}</view>
        <view class="agreement-body">
          <rich-text :nodes="agreementData.agreementContent || '暂无内容'"></rich-text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getAgreementByType } from '@/api/agreement'

export default {
  name: 'PrivacyAgreement',
  data() {
    return {
      loading: true,
      error: '',
      agreementData: {}
    }
  },
  
  onLoad() {
    this.loadAgreement()
  },
  
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 加载隐私政策
    async loadAgreement() {
      try {
        this.loading = true
        this.error = ''
        
        const response = await getAgreementByType(2) // 2表示隐私协议
        
        if (response.code === 200) {
          this.agreementData = response.data || {}
        } else {
          this.error = response.msg || '获取协议失败'
        }
      } catch (err) {
        console.error('加载隐私政策失败:', err)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.agreement-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #ebeef5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-back {
  font-size: 36rpx;
  color: #333;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.nav-placeholder {
  width: 60rpx;
}

.content-wrapper {
  padding: 32rpx;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.error-text {
  font-size: 28rpx;
  color: #f56c6c;
  margin-bottom: 32rpx;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background-color: #409eff;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.agreement-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.agreement-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #ebeef5;
}

.agreement-body {
  line-height: 1.8;
  color: #666;
  font-size: 28rpx;
}

/* rich-text 内容样式 */
.agreement-body :deep(p) {
  margin: 16rpx 0;
  line-height: 1.8;
}

.agreement-body :deep(strong) {
  font-weight: 600;
  color: #333;
}

.agreement-body :deep(h1),
.agreement-body :deep(h2),
.agreement-body :deep(h3) {
  font-weight: 600;
  color: #333;
  margin: 32rpx 0 16rpx 0;
}

.agreement-body :deep(h1) {
  font-size: 36rpx;
}

.agreement-body :deep(h2) {
  font-size: 32rpx;
}

.agreement-body :deep(h3) {
  font-size: 30rpx;
}
</style>
