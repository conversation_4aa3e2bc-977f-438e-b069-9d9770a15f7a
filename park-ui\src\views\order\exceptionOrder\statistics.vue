<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statisticsData.totalCount || 0 }}</div>
              <div class="statistics-label">异常订单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon pending">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statisticsData.pendingCount || 0 }}</div>
              <div class="statistics-label">待处理订单</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon today">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ statisticsData.todayNewCount || 0 }}</div>
              <div class="statistics-label">今日新增</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon processed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="statistics-info">
              <div class="statistics-number">{{ getProcessedCount() }}</div>
              <div class="statistics-label">已处理订单</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-container">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>异常类型分布</span>
            </div>
          </template>
          <div ref="typeChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>处理状态分布</span>
            </div>
          </template>
          <div ref="statusChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-container">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>异常订单趋势（最近30天）</span>
              <el-button-group>
                <el-button size="small" @click="changeTrendDays(7)" :type="trendDays === 7 ? 'primary' : ''">7天</el-button>
                <el-button size="small" @click="changeTrendDays(15)" :type="trendDays === 15 ? 'primary' : ''">15天</el-button>
                <el-button size="small" @click="changeTrendDays(30)" :type="trendDays === 30 ? 'primary' : ''">30天</el-button>
              </el-button-group>
            </div>
          </template>
          <div ref="trendChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 场库统计表格 -->
    <el-row class="chart-container">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>场库异常统计</span>
            </div>
          </template>
          <el-table :data="warehouseStats" style="width: 100%">
            <el-table-column prop="warehouseName" label="场库名称" />
            <el-table-column prop="count" label="异常订单数量" sortable />
            <el-table-column label="占比">
              <template #default="scope">
                <el-progress
                  :percentage="getPercentage(scope.row.count)"
                  :stroke-width="8"
                  :show-text="true"
                  :format="() => getPercentage(scope.row.count) + '%'"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="ExceptionOrderStatistics">
import * as echarts from 'echarts';
import { Document, Warning, Calendar, Check } from '@element-plus/icons-vue';
import {
  getExceptionOrderStatistics,
  getExceptionOrderTrend,
  getPendingExceptionOrderCount
} from "@/api/order/exceptionOrder";

const { proxy } = getCurrentInstance();

const statisticsData = ref({});
const typeStats = ref([]);
const statusStats = ref([]);
const warehouseStats = ref([]);
const trendData = ref([]);
const trendDays = ref(30);

const typeChartRef = ref();
const statusChartRef = ref();
const trendChartRef = ref();

let typeChart = null;
let statusChart = null;
let trendChart = null;

/** 获取统计数据 */
function getStatisticsData() {
  getExceptionOrderStatistics().then(response => {
    statisticsData.value = response.data;
    typeStats.value = response.data.typeStats || [];
    statusStats.value = response.data.statusStats || [];
    warehouseStats.value = response.data.warehouseStats || [];

    // 更新图表
    updateTypeChart();
    updateStatusChart();
  });
}

/** 获取趋势数据 */
function getTrendData() {
  getExceptionOrderTrend(trendDays.value).then(response => {
    trendData.value = response.data || [];
    updateTrendChart();
  });
}

/** 更新异常类型图表 */
function updateTypeChart() {
  if (!typeChart) {
    typeChart = echarts.init(typeChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '异常类型',
        type: 'pie',
        radius: '50%',
        data: typeStats.value.map(item => ({
          value: item.count,
          name: item.typeName
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  typeChart.setOption(option);
}

/** 更新处理状态图表 */
function updateStatusChart() {
  if (!statusChart) {
    statusChart = echarts.init(statusChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '处理状态',
        type: 'pie',
        radius: '50%',
        data: statusStats.value.map(item => ({
          value: item.count,
          name: item.statusName
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  statusChart.setOption(option);
}

/** 更新趋势图表 */
function updateTrendChart() {
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value);
  }

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['异常订单数量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: trendData.value.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '异常订单数量',
        type: 'line',
        stack: 'Total',
        data: trendData.value.map(item => item.count)
      }
    ]
  };

  trendChart.setOption(option);
}

/** 切换趋势天数 */
function changeTrendDays(days) {
  trendDays.value = days;
  getTrendData();
}

/** 获取已处理订单数量 */
function getProcessedCount() {
  const processedStatus = statusStats.value.find(item => item.status === 2);
  return processedStatus ? processedStatus.count : 0;
}

/** 计算百分比 */
function getPercentage(count) {
  const total = statisticsData.value.totalCount || 1;
  return Math.round((count / total) * 100);
}

/** 窗口大小改变时重新调整图表 */
function handleResize() {
  if (typeChart) typeChart.resize();
  if (statusChart) statusChart.resize();
  if (trendChart) trendChart.resize();
}

onMounted(() => {
  getStatisticsData();
  getTrendData();

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (typeChart) typeChart.dispose();
  if (statusChart) statusChart.dispose();
  if (trendChart) trendChart.dispose();
});
</script>

<style scoped>
.statistics-cards {
  margin-bottom: 20px;
}

.statistics-card {
  height: 120px;
}

.statistics-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.statistics-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 20px;
}

.statistics-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.statistics-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.statistics-icon.today {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.statistics-icon.processed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.statistics-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.chart-container {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
