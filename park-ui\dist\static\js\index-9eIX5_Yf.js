import{s as S,_ as ge,M as be,g as ye,r as m,A as ve,R as he,p as Ne,d as r,O as W,c as z,o as f,P as h,e as l,Q as Ve,k as t,f as a,l as B,q as s,i as V,N as E,t as b,h as M}from"./index-B3e47hUR.js";import{C as X}from"./index-DFSldapw.js";function ke(_){return S({url:"/system/special/user/list",method:"get",params:_})}function Y(_){return S({url:"/system/special/user/"+_,method:"get"})}function we(_){return S({url:"/system/special/user",method:"post",data:_})}function Ue(_){return S({url:"/system/special/user",method:"put",data:_})}function Ce(_){return S({url:"/system/special/user/"+_,method:"delete"})}const Te={class:"app-container"},Se={class:"form-notice"},xe={key:0},Pe={key:1},$e={class:"dialog-footer"},De={class:"dialog-footer"},Re=be({name:"SpecialUser"}),qe=Object.assign(Re,{components:{CustomPagination:X}},{setup(_){const{proxy:y}=ye(),K=m([]),N=m(!1),C=m(!1),R=m(!0),x=m(!0),P=m([]),Q=m(!0),F=m(!0),O=m(0),q=m(""),g=m({}),Z=ve({form:{},queryParams:{pageNum:1,pageSize:10,nickName:null,phoneNumber:null,plateNo:null,userType:null},rules:{phoneNumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],userType:[{required:!0,message:"用户类型不能为空",trigger:"blur"}]}}),{queryParams:u,form:i,rules:ee}=he(Z);function le(n){return{VIP客户:"warning",集团客户:"warning"}[n]||"info"}function k(){R.value=!0,ke(u.value).then(n=>{K.value=n.rows,O.value=n.total,R.value=!1})}function I(){i.value={id:null,nickName:null,phoneNumber:null,plateNo:null,userType:null},y.resetForm("specialUserRef")}function T(){u.value.pageNum=1,k()}function ae(){y.resetForm("queryRef"),T()}function te(n){P.value=n.map(o=>o.id),Q.value=n.length!=1,F.value=!n.length}function oe(n){const o=n.id||P.value;Y(o).then(d=>{g.value=d.data||{},C.value=!0}).catch(d=>{console.error("获取特殊会员详情失败:",d),y.$modal.msgError("获取特殊会员详情失败")})}function ne(){I(),N.value=!0,q.value="添加特殊会员"}function A(n){I();const o=n.id||P.value;Y(o).then(d=>{i.value=d.data||{},N.value=!0,q.value="修改特殊会员"})}function se(){y.$refs.specialUserRef.validate(n=>{n&&(i.value.id!=null?Ue(i.value).then(o=>{y.$modal.msgSuccess("修改成功"),N.value=!1,k()}):we(i.value).then(o=>{y.$modal.msgSuccess("新增成功"),N.value=!1,k()}))})}function ue(){N.value=!1,I()}function L(n){const o=n.id||P.value,c=`删除特殊会员前，请先删除该特殊用户所有的特殊会员车辆，才能删除该手机号用户，同时会将该手机号对应的系统用户改为普通用户。

是否确认删除特殊会员"${n?n.nickName||n.phoneNumber:"选中的特殊会员"}"？`;y.$modal.confirm(c).then(function(){return Ce(o)}).then(()=>{k(),y.$modal.msgSuccess("删除成功")}).catch(()=>{})}function re(){y.download("special/user/export",{...u.value},`特殊会员_${new Date().getTime()}.xlsx`)}return Ne(()=>{k()}),(n,o)=>{const d=r("el-input"),c=r("el-form-item"),$=r("el-option"),j=r("el-select"),p=r("el-button"),G=r("el-form"),D=r("el-col"),ie=r("right-toolbar"),de=r("el-row"),U=r("el-table-column"),pe=r("el-tag"),ce=r("el-table"),me=r("el-alert"),H=r("el-dialog"),v=r("el-descriptions-item"),fe=r("el-descriptions"),w=W("hasPermi"),_e=W("loading");return f(),z("div",Te,[h(l(G,{model:t(u),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[l(c,{label:"昵称",prop:"nickName"},{default:a(()=>[l(d,{modelValue:t(u).nickName,"onUpdate:modelValue":o[0]||(o[0]=e=>t(u).nickName=e),placeholder:"请输入姓名",clearable:"",style:{width:"200px"},onKeyup:B(T,["enter"])},null,8,["modelValue"])]),_:1}),l(c,{label:"手机号码",prop:"phoneNumber"},{default:a(()=>[l(d,{modelValue:t(u).phoneNumber,"onUpdate:modelValue":o[1]||(o[1]=e=>t(u).phoneNumber=e),placeholder:"请输入手机号码",clearable:"",style:{width:"200px"},onKeyup:B(T,["enter"])},null,8,["modelValue"])]),_:1}),l(c,{label:"车牌号",prop:"plateNo"},{default:a(()=>[l(d,{modelValue:t(u).plateNo,"onUpdate:modelValue":o[2]||(o[2]=e=>t(u).plateNo=e),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:B(T,["enter"])},null,8,["modelValue"])]),_:1}),l(c,{label:"用户类型",prop:"userType"},{default:a(()=>[l(j,{modelValue:t(u).userType,"onUpdate:modelValue":o[3]||(o[3]=e=>t(u).userType=e),placeholder:"请选择用户类型",clearable:"",style:{width:"200px"}},{default:a(()=>[l($,{label:"VIP客户",value:"VIP客户"}),l($,{label:"集团客户",value:"集团客户"})]),_:1},8,["modelValue"])]),_:1}),l(c,null,{default:a(()=>[l(p,{type:"primary",icon:"Search",onClick:T},{default:a(()=>[s("搜索")]),_:1}),l(p,{icon:"Refresh",onClick:ae},{default:a(()=>[s("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Ve,t(x)]]),l(de,{gutter:10,class:"mb8"},{default:a(()=>[l(D,{span:1.5},{default:a(()=>[h((f(),V(p,{type:"primary",plain:"",icon:"Plus",onClick:ne},{default:a(()=>[s("新增")]),_:1})),[[w,["special:user:add"]]])]),_:1}),l(D,{span:1.5},{default:a(()=>[h((f(),V(p,{type:"success",plain:"",icon:"Edit",disabled:t(Q),onClick:A},{default:a(()=>[s("修改")]),_:1},8,["disabled"])),[[w,["special:user:edit"]]])]),_:1}),l(D,{span:1.5},{default:a(()=>[h((f(),V(p,{type:"danger",plain:"",icon:"Delete",disabled:t(F),onClick:L},{default:a(()=>[s("删除")]),_:1},8,["disabled"])),[[w,["special:user:remove"]]])]),_:1}),l(D,{span:1.5},{default:a(()=>[h((f(),V(p,{type:"warning",plain:"",icon:"Download",onClick:re},{default:a(()=>[s("导出")]),_:1})),[[w,["special:user:export"]]])]),_:1}),l(ie,{showSearch:t(x),"onUpdate:showSearch":o[4]||(o[4]=e=>E(x)?x.value=e:null),onQueryTable:k},null,8,["showSearch"])]),_:1}),h((f(),V(ce,{data:t(K),onSelectionChange:te},{default:a(()=>[l(U,{type:"selection",width:"55",align:"center"}),l(U,{label:"昵称",align:"center",prop:"nickName"}),l(U,{label:"手机号码",align:"center",prop:"phoneNumber"}),l(U,{label:"用户类型",align:"center",prop:"userType"},{default:a(e=>[l(pe,{type:le(e.row.userType)},{default:a(()=>[s(b(e.row.userType),1)]),_:2},1032,["type"])]),_:1}),l(U,{label:"车牌号",align:"center",prop:"plateNo"}),l(U,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(e=>[h((f(),V(p,{link:"",type:"primary",icon:"View",onClick:J=>oe(e.row)},{default:a(()=>[s("查看")]),_:2},1032,["onClick"])),[[w,["special:user:query"]]]),h((f(),V(p,{link:"",type:"primary",icon:"Edit",onClick:J=>A(e.row)},{default:a(()=>[s("修改")]),_:2},1032,["onClick"])),[[w,["special:user:edit"]]]),h((f(),V(p,{link:"",type:"primary",icon:"Delete",onClick:J=>L(e.row)},{default:a(()=>[s("删除")]),_:2},1032,["onClick"])),[[w,["special:user:remove"]]])]),_:1})]),_:1},8,["data"])),[[_e,t(R)]]),l(X,{total:t(O),"current-page":t(u).pageNum,"onUpdate:currentPage":o[5]||(o[5]=e=>t(u).pageNum=e),"page-size":t(u).pageSize,"onUpdate:pageSize":o[6]||(o[6]=e=>t(u).pageSize=e),onPagination:k},null,8,["total","current-page","page-size"]),l(H,{title:t(q),modelValue:t(N),"onUpdate:modelValue":o[11]||(o[11]=e=>E(N)?N.value=e:null),width:"600px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:a(()=>[M("div",$e,[l(p,{type:"primary",onClick:se},{default:a(()=>[s("确 定")]),_:1}),l(p,{onClick:ue},{default:a(()=>[s("取 消")]),_:1})])]),default:a(()=>[l(G,{ref:"specialUserRef",model:t(i),rules:t(ee),"label-width":"100px"},{default:a(()=>[l(c,{label:"姓名",prop:"nickName"},{default:a(()=>[l(d,{modelValue:t(i).nickName,"onUpdate:modelValue":o[7]||(o[7]=e=>t(i).nickName=e),placeholder:"请输入真实姓名"},null,8,["modelValue"])]),_:1}),l(c,{label:"手机号码",prop:"phoneNumber"},{default:a(()=>[l(d,{modelValue:t(i).phoneNumber,"onUpdate:modelValue":o[8]||(o[8]=e=>t(i).phoneNumber=e),placeholder:"请输入手机号码"},null,8,["modelValue"])]),_:1}),l(c,{label:"用户类型",prop:"userType"},{default:a(()=>[l(j,{modelValue:t(i).userType,"onUpdate:modelValue":o[9]||(o[9]=e=>t(i).userType=e),placeholder:"请选择用户类型",style:{width:"100%"}},{default:a(()=>[l($,{label:"VIP客户",value:"VIP客户"}),l($,{label:"集团客户",value:"集团客户"})]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"车牌号",prop:"plateNo"},{default:a(()=>[l(d,{modelValue:t(i).plateNo,"onUpdate:modelValue":o[10]||(o[10]=e=>t(i).plateNo=e),placeholder:"请输入车牌号"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),M("div",Se,[l(me,{type:"info",closable:!1,"show-icon":""},{default:a(()=>[t(i).id?(f(),z("span",Pe," 注：如果只修改用户类型，系统会将该手机号对应的用户改为对应的特殊会员类型。如果修改手机号，系统会将该手机号对应的用户手机号， 和对应的特殊会员车辆的手机号一起修改，请谨慎操作。 ")):(f(),z("span",xe," 注：添加特殊会员时，会将系统中该手机号用户变为对应的特殊用户，如果该用户不存在，会新增失败，请确保用户已在系统中注册。 "))]),_:1})])]),_:1},8,["title","modelValue"]),l(H,{title:"特殊会员详情",modelValue:t(C),"onUpdate:modelValue":o[13]||(o[13]=e=>E(C)?C.value=e:null),width:"600px","append-to-body":""},{footer:a(()=>[M("div",De,[l(p,{onClick:o[12]||(o[12]=e=>C.value=!1)},{default:a(()=>[s("关 闭")]),_:1})])]),default:a(()=>[l(fe,{column:2,border:""},{default:a(()=>[l(v,{label:"ID"},{default:a(()=>{var e;return[s(b(((e=t(g))==null?void 0:e.id)||"-"),1)]}),_:1}),l(v,{label:"姓名"},{default:a(()=>{var e;return[s(b(((e=t(g))==null?void 0:e.nickName)||"-"),1)]}),_:1}),l(v,{label:"手机号码"},{default:a(()=>{var e;return[s(b(((e=t(g))==null?void 0:e.phoneNumber)||"-"),1)]}),_:1}),l(v,{label:"车牌号"},{default:a(()=>{var e;return[s(b(((e=t(g))==null?void 0:e.plateNo)||"-"),1)]}),_:1}),l(v,{label:"用户类型",span:2},{default:a(()=>{var e;return[s(b(((e=t(g))==null?void 0:e.userType)||"-"),1)]}),_:1}),l(v,{label:"创建者"},{default:a(()=>{var e;return[s(b(((e=t(g))==null?void 0:e.createByName)||"-"),1)]}),_:1}),l(v,{label:"更新者"},{default:a(()=>{var e;return[s(b(((e=t(g))==null?void 0:e.updateByName)||"-"),1)]}),_:1}),l(v,{label:"创建时间"},{default:a(()=>{var e;return[s(b(n.parseTime((e=t(g))==null?void 0:e.createTime)),1)]}),_:1}),l(v,{label:"更新时间"},{default:a(()=>{var e;return[s(b(n.parseTime((e=t(g))==null?void 0:e.updateTime)),1)]}),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),Be=ge(qe,[["__scopeId","data-v-d59c9f84"]]);export{Be as default};
