import{s as x,_ as _e,M as ve,g as we,r as i,A as be,R as ye,d as p,O as G,c as P,o as s,P as w,e as a,Q as Ve,k as o,f as t,J as $,K as q,i as m,l as H,q as f,N as X,h as Y,t as ke}from"./index-B3e47hUR.js";import{o as Ce}from"./operator-b9e2JnZC.js";import{o as Z,a as E}from"./warehouse-I5QKWbAT.js";import{C as ee}from"./index-DFSldapw.js";function Ne(c){return x({url:"/system/platform/warehouseManager/list",method:"get",params:c})}function Ie(c){return x({url:"/system/platform/warehouseManager/"+c,method:"get"})}function Pe(c){return x({url:"/system/platform/warehouseManager",method:"post",data:c})}function xe(c){return x({url:"/system/platform/warehouseManager",method:"put",data:c})}function Me(c){return x({url:"/system/platform/warehouseManager/"+c,method:"delete"})}const Se={class:"app-container"},Oe={class:"dialog-footer"},Ue=ve({name:"WarehouseManager"}),We=Object.assign(Ue,{components:{CustomPagination:ee}},{setup(c){const{proxy:h}=we(),F=i([]),N=i([]),M=i([]),V=i([]),_=i(!1),S=i(!0),O=i(!0),R=i([]),K=i(!0),Q=i(!0),L=i(0),D=i(""),ae=be({form:{},queryParams:{pageNum:1,pageSize:10,operatorId:null,warehouseId:null,managerName:null,managerPhone:null},rules:{operatorId:[{required:!0,message:"所属运营商不能为空",trigger:"change"}],warehouseId:[{required:!0,message:"负责场库不能为空",trigger:"change"}],managerName:[{required:!0,message:"管理人员姓名不能为空",trigger:"blur"}],managerPhone:[{required:!0,message:"管理人员电话不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}),{queryParams:u,form:r,rules:le}=ye(ae);function b(){S.value=!0,Ne(u.value).then(n=>{F.value=n.rows,L.value=n.total,S.value=!1}).catch(n=>{console.error("获取场库管理人员列表失败:",n),S.value=!1})}function z(){return Ce().then(n=>(N.value=n.data,n)).catch(n=>{throw console.error("获取运营商选项失败:",n),n})}function T(){Z().then(n=>{M.value=n.data})}function oe(n){u.value.warehouseId=null,n?E(n).then(l=>{M.value=l.data}):T()}function te(n){r.value.warehouseId=null,n?E(n).then(l=>{V.value=l.data}):V.value=[]}function ne(){_.value=!1,B()}function B(){r.value={id:null,operatorId:null,warehouseId:null,managerName:null,managerPhone:null,remark:null},V.value=[],h.resetForm("managerRef")}function U(){u.value.pageNum=1,b()}function re(){h.resetForm("queryRef"),M.value=[],T(),U()}function ue(n){R.value=n.map(l=>l.id),K.value=n.length!=1,Q.value=!n.length}function se(){B(),N.value.length===0&&z(),_.value=!0,D.value="添加场库管理人员信息"}function A(n){B();const l=n.id||R.value,k=N.value.length===0?z():Promise.resolve();Promise.resolve(k).then(()=>Ie(l)).then(v=>(r.value=v.data,r.value.operatorId?E(r.value.operatorId).then(d=>{V.value=d.data}):Z().then(d=>{V.value=d.data}))).then(()=>{_.value=!0,D.value="修改场库管理人员信息"}).catch(v=>{console.error("修改操作失败:",v),h.$modal.msgError("获取数据失败，请重试")})}function de(){h.$refs.managerRef.validate(n=>{n&&(r.value.id!=null?xe(r.value).then(l=>{h.$modal.msgSuccess("修改成功"),_.value=!1,b()}):Pe(r.value).then(l=>{h.$modal.msgSuccess("新增成功"),_.value=!1,b()}))})}function j(n){const l=n.id||R.value;h.$modal.confirm('是否确认删除场库管理人员信息编号为"'+l+'"的数据项？').then(function(){return Me(l)}).then(()=>{b(),h.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ie(){h.download("system/platform/warehouseManager/export",{...u.value},`warehouseManager_${new Date().getTime()}.xlsx`)}return b(),z(),T(),(n,l)=>{const k=p("el-option"),v=p("el-select"),d=p("el-form-item"),I=p("el-input"),g=p("el-button"),J=p("el-form"),W=p("el-col"),pe=p("right-toolbar"),me=p("el-row"),y=p("el-table-column"),ce=p("el-table"),ge=p("el-dialog"),C=G("hasPermi"),fe=G("loading");return s(),P("div",Se,[w(a(J,{model:o(u),ref:"queryRef",inline:!0,"label-width":"68px"},{default:t(()=>[a(d,{label:"运营商",prop:"operatorId"},{default:t(()=>[a(v,{modelValue:o(u).operatorId,"onUpdate:modelValue":l[0]||(l[0]=e=>o(u).operatorId=e),placeholder:"请选择运营商",clearable:"",onChange:oe,style:{width:"280px"},"popper-class":"operator-select-dropdown"},{default:t(()=>[(s(!0),P($,null,q(o(N),e=>(s(),m(k,{key:e.id,label:e.companyName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"场库",prop:"warehouseId"},{default:t(()=>[a(v,{modelValue:o(u).warehouseId,"onUpdate:modelValue":l[1]||(l[1]=e=>o(u).warehouseId=e),placeholder:"请选择场库",clearable:"",style:{width:"200px"},"popper-class":"warehouse-select-dropdown"},{default:t(()=>[(s(!0),P($,null,q(o(M),e=>(s(),m(k,{key:e.id,label:e.warehouseName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"管理人员",prop:"managerName"},{default:t(()=>[a(I,{modelValue:o(u).managerName,"onUpdate:modelValue":l[2]||(l[2]=e=>o(u).managerName=e),placeholder:"请输入管理人员姓名",clearable:"",style:{width:"200px"},onKeyup:H(U,["enter"])},null,8,["modelValue"])]),_:1}),a(d,{label:"联系电话",prop:"managerPhone"},{default:t(()=>[a(I,{modelValue:o(u).managerPhone,"onUpdate:modelValue":l[3]||(l[3]=e=>o(u).managerPhone=e),placeholder:"请输入管理人员电话",clearable:"",style:{width:"200px"},onKeyup:H(U,["enter"])},null,8,["modelValue"])]),_:1}),a(d,null,{default:t(()=>[a(g,{type:"primary",icon:"Search",onClick:U},{default:t(()=>[f("搜索")]),_:1}),a(g,{icon:"Refresh",onClick:re},{default:t(()=>[f("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Ve,o(O)]]),a(me,{gutter:10,class:"mb8"},{default:t(()=>[a(W,{span:1.5},{default:t(()=>[w((s(),m(g,{type:"primary",plain:"",icon:"Plus",onClick:se},{default:t(()=>[f("新增")]),_:1})),[[C,["platform:warehouseManager:add"]]])]),_:1}),a(W,{span:1.5},{default:t(()=>[w((s(),m(g,{type:"success",plain:"",icon:"Edit",disabled:o(K),onClick:A},{default:t(()=>[f("修改")]),_:1},8,["disabled"])),[[C,["platform:warehouseManager:edit"]]])]),_:1}),a(W,{span:1.5},{default:t(()=>[w((s(),m(g,{type:"danger",plain:"",icon:"Delete",disabled:o(Q),onClick:j},{default:t(()=>[f("删除")]),_:1},8,["disabled"])),[[C,["platform:warehouseManager:remove"]]])]),_:1}),a(W,{span:1.5},{default:t(()=>[w((s(),m(g,{type:"warning",plain:"",icon:"Download",onClick:ie},{default:t(()=>[f("导出")]),_:1})),[[C,["platform:warehouseManager:export"]]])]),_:1}),a(pe,{showSearch:o(O),"onUpdate:showSearch":l[4]||(l[4]=e=>X(O)?O.value=e:null),onQueryTable:b},null,8,["showSearch"])]),_:1}),w((s(),m(ce,{data:o(F),onSelectionChange:ue},{default:t(()=>[a(y,{type:"selection",width:"55",align:"center"}),a(y,{label:"所属运营商",align:"center",prop:"operatorName","show-overflow-tooltip":!0}),a(y,{label:"负责场库",align:"center",prop:"warehouseName","show-overflow-tooltip":!0}),a(y,{label:"管理人员",align:"center",prop:"managerName"}),a(y,{label:"联系电话",align:"center",prop:"managerPhone"}),a(y,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:t(e=>[Y("span",null,ke(n.parseTime(e.row.createTime)),1)]),_:1}),a(y,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(e=>[w((s(),m(g,{link:"",type:"primary",icon:"Edit",onClick:he=>A(e.row)},{default:t(()=>[f("修改")]),_:2},1032,["onClick"])),[[C,["platform:warehouseManager:edit"]]]),w((s(),m(g,{link:"",type:"primary",icon:"Delete",onClick:he=>j(e.row)},{default:t(()=>[f("删除")]),_:2},1032,["onClick"])),[[C,["platform:warehouseManager:remove"]]])]),_:1})]),_:1},8,["data"])),[[fe,o(S)]]),a(ee,{total:o(L),"current-page":o(u).pageNum,"onUpdate:currentPage":l[5]||(l[5]=e=>o(u).pageNum=e),"page-size":o(u).pageSize,"onUpdate:pageSize":l[6]||(l[6]=e=>o(u).pageSize=e),onPagination:b},null,8,["total","current-page","page-size"]),a(ge,{title:o(D),modelValue:o(_),"onUpdate:modelValue":l[12]||(l[12]=e=>X(_)?_.value=e:null),width:"600px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:t(()=>[Y("div",Oe,[a(g,{type:"primary",onClick:de},{default:t(()=>[f("确 定")]),_:1}),a(g,{onClick:ne},{default:t(()=>[f("取 消")]),_:1})])]),default:t(()=>[a(J,{ref:"managerRef",model:o(r),rules:o(le),"label-width":"120px"},{default:t(()=>[a(d,{label:"所属运营商",prop:"operatorId"},{default:t(()=>[a(v,{modelValue:o(r).operatorId,"onUpdate:modelValue":l[7]||(l[7]=e=>o(r).operatorId=e),placeholder:"请选择运营商",onChange:te,style:{width:"100%"},"popper-class":"operator-select-dropdown"},{default:t(()=>[(s(!0),P($,null,q(o(N),e=>(s(),m(k,{key:e.id,label:e.companyName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"负责场库",prop:"warehouseId"},{default:t(()=>[a(v,{modelValue:o(r).warehouseId,"onUpdate:modelValue":l[8]||(l[8]=e=>o(r).warehouseId=e),placeholder:"请选择场库",style:{width:"100%"},"popper-class":"warehouse-select-dropdown"},{default:t(()=>[(s(!0),P($,null,q(o(V),e=>(s(),m(k,{key:e.id,label:e.warehouseName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"管理人员姓名",prop:"managerName"},{default:t(()=>[a(I,{modelValue:o(r).managerName,"onUpdate:modelValue":l[9]||(l[9]=e=>o(r).managerName=e),placeholder:"请输入管理人员姓名"},null,8,["modelValue"])]),_:1}),a(d,{label:"管理人员电话",prop:"managerPhone"},{default:t(()=>[a(I,{modelValue:o(r).managerPhone,"onUpdate:modelValue":l[10]||(l[10]=e=>o(r).managerPhone=e),placeholder:"请输入管理人员电话"},null,8,["modelValue"])]),_:1}),a(d,{label:"备注",prop:"remark"},{default:t(()=>[a(I,{modelValue:o(r).remark,"onUpdate:modelValue":l[11]||(l[11]=e=>o(r).remark=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),ze=_e(We,[["__scopeId","data-v-a0e622a9"]]);export{ze as default};
