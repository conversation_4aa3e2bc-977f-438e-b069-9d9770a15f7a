import{M as He,a as Je,g as Ge,r as s,A as We,R as Xe,d as r,O as me,c as L,o as m,P as h,e as t,Q as ce,k as a,f as o,l as fe,J as W,K as X,i as y,N as V,q as i,h as O,t as ve,j as F,B as Q}from"./index-B3e47hUR.js";import{l as Ze,g as ye,e as el,f as ll,h as tl,i as al,j as ol,k as nl}from"./role--2_6yfYL.js";import{t as ul,r as dl}from"./menu-5r409o94.js";import{C as he}from"./index-DFSldapw.js";const rl={class:"app-container"},sl={class:"dialog-footer"},il={class:"dialog-footer"},pl=He({name:"Role"}),hl=Object.assign(pl,{components:{CustomPagination:he}},{setup(ml){const _e=Je(),{proxy:v}=Ge(),{sys_normal_disable:Z}=v.useDict("sys_normal_disable"),ee=s([]),_=s(!1),Y=s(!0),T=s(!0),j=s([]),le=s(!0),te=s(!0),ae=s(0),N=s(""),U=s([]),R=s([]),$=s(!1),q=s(!1),M=s(!0),P=s(!1),A=s([]),w=s(!1),g=s(null),k=s(null),ge=s([{value:"1",label:"全部数据权限"},{value:"2",label:"自定数据权限"},{value:"3",label:"本部门数据权限"},{value:"4",label:"本部门及以下数据权限"},{value:"5",label:"仅本人数据权限"}]),ke=We({form:{},queryParams:{pageNum:1,pageSize:10,roleName:void 0,roleKey:void 0,status:void 0},rules:{roleName:[{required:!0,message:"角色名称不能为空",trigger:"blur"}],roleKey:[{required:!0,message:"权限字符不能为空",trigger:"blur"}],roleSort:[{required:!0,message:"角色顺序不能为空",trigger:"blur"}]}}),{queryParams:c,form:u,rules:be}=Xe(ke);function b(){Y.value=!0,Ze(v.addDateRange(c.value,U.value)).then(n=>{ee.value=n.rows,ae.value=n.total,Y.value=!1})}function E(){c.value.pageNum=1,b()}function Ce(){U.value=[],v.resetForm("queryRef"),E()}function oe(n){const e=n.roleId||j.value;v.$modal.confirm('是否确认删除角色编号为"'+e+'"的数据项?').then(function(){return el(e)}).then(()=>{b(),v.$modal.msgSuccess("删除成功")}).catch(()=>{})}function Ve(){v.download("system/role/export",{...c.value},`role_${new Date().getTime()}.xlsx`)}function Se(n){j.value=n.map(e=>e.roleId),le.value=n.length!=1,te.value=!n.length}function we(n){let e=n.status==="0"?"启用":"停用";v.$modal.confirm('确认要"'+e+'""'+n.roleName+'"角色吗?').then(function(){return ll(n.roleId,n.status)}).then(()=>{v.$modal.msgSuccess(e+"成功")}).catch(function(){n.status=n.status==="0"?"1":"0"})}function xe(n){_e.push("/system/role-auth/user/"+n.roleId)}function Ke(){ul().then(n=>{R.value=n.data})}function Ne(){let n=k.value.getCheckedKeys(),e=k.value.getHalfCheckedKeys();return n.unshift.apply(n,e),n}function I(){g.value!=null&&g.value.setCheckedKeys([]),$.value=!1,q.value=!1,M.value=!0,P.value=!1,u.value={roleId:void 0,roleName:void 0,roleKey:void 0,roleSort:0,status:"0",menuIds:[],deptIds:[],menuCheckStrictly:!0,deptCheckStrictly:!0,remark:void 0},v.resetForm("roleRef")}function Ue(){I(),Ke(),_.value=!0,N.value="添加角色"}function ne(n){I();const e=n.roleId||j.value,p=Re(e);ye(e).then(d=>{u.value=d.data,u.value.roleSort=Number(u.value.roleSort),_.value=!0,Q(()=>{p.then(z=>{z.checkedKeys.forEach(J=>{Q(()=>{g.value.setChecked(J,!0,!1)})})})})}),N.value="修改角色"}function Re(n){return dl(n).then(e=>(R.value=e.menus,e))}function Ie(n){return nl(n).then(e=>(A.value=e.depts,e))}function ue(n,e){if(e=="menu"){let p=R.value;for(let d=0;d<p.length;d++)g.value.store.nodesMap[p[d].id].expanded=n}else if(e=="dept"){let p=A.value;for(let d=0;d<p.length;d++)k.value.store.nodesMap[p[d].id].expanded=n}}function de(n,e){e=="menu"?g.value.setCheckedNodes(n?R.value:[]):e=="dept"&&k.value.setCheckedNodes(n?A.value:[])}function re(n,e){e=="menu"?u.value.menuCheckStrictly=!!n:e=="dept"&&(u.value.deptCheckStrictly=!!n)}function se(){let n=g.value.getCheckedKeys(),e=g.value.getHalfCheckedKeys();return n.unshift.apply(n,e),n}function De(){v.$refs.roleRef.validate(n=>{n&&(u.value.roleId!=null?(u.value.menuIds=se(),tl(u.value).then(e=>{v.$modal.msgSuccess("修改成功"),_.value=!1,b()})):(u.value.menuIds=se(),al(u.value).then(e=>{v.$modal.msgSuccess("新增成功"),_.value=!1,b()})))})}function Te(){_.value=!1,I()}function $e(n){n!=="2"&&k.value.setCheckedKeys([])}function qe(n){I();const e=Ie(n.roleId);ye(n.roleId).then(p=>{u.value=p.data,w.value=!0,Q(()=>{e.then(d=>{Q(()=>{k.value&&k.value.setCheckedKeys(d.checkedKeys)})})})}),N.value="分配数据权限"}function Me(){u.value.roleId!=null&&(u.value.deptIds=Ne(),ol(u.value).then(n=>{v.$modal.msgSuccess("修改成功"),w.value=!1,b()}))}function Pe(){w.value=!1,I()}return b(),(n,e)=>{const p=r("el-input"),d=r("el-form-item"),z=r("el-option"),H=r("el-select"),J=r("el-date-picker"),f=r("el-button"),G=r("el-form"),B=r("el-col"),Ae=r("right-toolbar"),Ee=r("el-row"),S=r("el-table-column"),ze=r("el-switch"),D=r("el-tooltip"),Be=r("el-table"),Le=r("question-filled"),Oe=r("el-icon"),Fe=r("el-input-number"),Qe=r("el-radio"),Ye=r("el-radio-group"),x=r("el-checkbox"),ie=r("el-tree"),pe=r("el-dialog"),C=me("hasPermi"),je=me("loading");return m(),L("div",rl,[h(t(G,{model:a(c),ref:"queryRef",inline:!0,"label-width":"68px"},{default:o(()=>[t(d,{label:"角色名称",prop:"roleName"},{default:o(()=>[t(p,{modelValue:a(c).roleName,"onUpdate:modelValue":e[0]||(e[0]=l=>a(c).roleName=l),placeholder:"请输入角色名称",clearable:"",style:{width:"240px"},onKeyup:fe(E,["enter"])},null,8,["modelValue"])]),_:1}),t(d,{label:"权限字符",prop:"roleKey"},{default:o(()=>[t(p,{modelValue:a(c).roleKey,"onUpdate:modelValue":e[1]||(e[1]=l=>a(c).roleKey=l),placeholder:"请输入权限字符",clearable:"",style:{width:"240px"},onKeyup:fe(E,["enter"])},null,8,["modelValue"])]),_:1}),t(d,{label:"状态",prop:"status"},{default:o(()=>[t(H,{modelValue:a(c).status,"onUpdate:modelValue":e[2]||(e[2]=l=>a(c).status=l),placeholder:"角色状态",clearable:"",style:{width:"240px"}},{default:o(()=>[(m(!0),L(W,null,X(a(Z),l=>(m(),y(z,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(d,{label:"创建时间",style:{width:"308px"}},{default:o(()=>[t(J,{modelValue:a(U),"onUpdate:modelValue":e[3]||(e[3]=l=>V(U)?U.value=l:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),t(d,null,{default:o(()=>[t(f,{type:"primary",icon:"Search",onClick:E},{default:o(()=>[i("搜索")]),_:1}),t(f,{icon:"Refresh",onClick:Ce},{default:o(()=>[i("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ce,a(T)]]),t(Ee,{gutter:10,class:"mb8"},{default:o(()=>[t(B,{span:1.5},{default:o(()=>[h((m(),y(f,{type:"primary",plain:"",icon:"Plus",onClick:Ue},{default:o(()=>[i("新增")]),_:1})),[[C,["system:role:add"]]])]),_:1}),t(B,{span:1.5},{default:o(()=>[h((m(),y(f,{type:"success",plain:"",icon:"Edit",disabled:a(le),onClick:ne},{default:o(()=>[i("修改")]),_:1},8,["disabled"])),[[C,["system:role:edit"]]])]),_:1}),t(B,{span:1.5},{default:o(()=>[h((m(),y(f,{type:"danger",plain:"",icon:"Delete",disabled:a(te),onClick:oe},{default:o(()=>[i("删除")]),_:1},8,["disabled"])),[[C,["system:role:remove"]]])]),_:1}),t(B,{span:1.5},{default:o(()=>[h((m(),y(f,{type:"warning",plain:"",icon:"Download",onClick:Ve},{default:o(()=>[i("导出")]),_:1})),[[C,["system:role:export"]]])]),_:1}),t(Ae,{showSearch:a(T),"onUpdate:showSearch":e[4]||(e[4]=l=>V(T)?T.value=l:null),onQueryTable:b},null,8,["showSearch"])]),_:1}),h((m(),y(Be,{data:a(ee),onSelectionChange:Se},{default:o(()=>[t(S,{type:"selection",width:"55",align:"center"}),t(S,{label:"角色名称",prop:"roleName","show-overflow-tooltip":!0,width:"150"}),t(S,{label:"权限字符",prop:"roleKey","show-overflow-tooltip":!0,width:"150"}),t(S,{label:"显示顺序",prop:"roleSort",width:"100"}),t(S,{label:"状态",align:"center",width:"100"},{default:o(l=>[t(ze,{modelValue:l.row.status,"onUpdate:modelValue":K=>l.row.status=K,"active-value":"0","inactive-value":"1",onChange:K=>we(l.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(S,{label:"创建时间",align:"center",prop:"createTime"},{default:o(l=>[O("span",null,ve(n.parseTime(l.row.createTime)),1)]),_:1}),t(S,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:o(l=>[l.row.roleId!==1?(m(),y(D,{key:0,content:"修改",placement:"top"},{default:o(()=>[h(t(f,{link:"",type:"primary",icon:"Edit",onClick:K=>ne(l.row)},null,8,["onClick"]),[[C,["system:role:edit"]]])]),_:2},1024)):F("",!0),l.row.roleId!==1?(m(),y(D,{key:1,content:"删除",placement:"top"},{default:o(()=>[h(t(f,{link:"",type:"primary",icon:"Delete",onClick:K=>oe(l.row)},null,8,["onClick"]),[[C,["system:role:remove"]]])]),_:2},1024)):F("",!0),l.row.roleId!==1?(m(),y(D,{key:2,content:"数据权限",placement:"top"},{default:o(()=>[h(t(f,{link:"",type:"primary",icon:"CircleCheck",onClick:K=>qe(l.row)},null,8,["onClick"]),[[C,["system:role:edit"]]])]),_:2},1024)):F("",!0),l.row.roleId!==1?(m(),y(D,{key:3,content:"分配用户",placement:"top"},{default:o(()=>[h(t(f,{link:"",type:"primary",icon:"User",onClick:K=>xe(l.row)},null,8,["onClick"]),[[C,["system:role:edit"]]])]),_:2},1024)):F("",!0)]),_:1})]),_:1},8,["data"])),[[je,a(Y)]]),t(he,{total:a(ae),"current-page":a(c).pageNum,"onUpdate:currentPage":e[5]||(e[5]=l=>a(c).pageNum=l),"page-size":a(c).pageSize,"onUpdate:pageSize":e[6]||(e[6]=l=>a(c).pageSize=l),onPagination:b},null,8,["total","current-page","page-size"]),t(pe,{title:a(N),modelValue:a(_),"onUpdate:modelValue":e[18]||(e[18]=l=>V(_)?_.value=l:null),width:"500px","append-to-body":""},{footer:o(()=>[O("div",sl,[t(f,{type:"primary",onClick:De},{default:o(()=>[i("确 定")]),_:1}),t(f,{onClick:Te},{default:o(()=>[i("取 消")]),_:1})])]),default:o(()=>[t(G,{ref:"roleRef",model:a(u),rules:a(be),"label-width":"100px"},{default:o(()=>[t(d,{label:"角色名称",prop:"roleName"},{default:o(()=>[t(p,{modelValue:a(u).roleName,"onUpdate:modelValue":e[7]||(e[7]=l=>a(u).roleName=l),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),t(d,{prop:"roleKey"},{label:o(()=>[O("span",null,[t(D,{content:"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)",placement:"top"},{default:o(()=>[t(Oe,null,{default:o(()=>[t(Le)]),_:1})]),_:1}),i(" 权限字符 ")])]),default:o(()=>[t(p,{modelValue:a(u).roleKey,"onUpdate:modelValue":e[8]||(e[8]=l=>a(u).roleKey=l),placeholder:"请输入权限字符"},null,8,["modelValue"])]),_:1}),t(d,{label:"角色顺序",prop:"roleSort"},{default:o(()=>[t(Fe,{modelValue:a(u).roleSort,"onUpdate:modelValue":e[9]||(e[9]=l=>a(u).roleSort=l),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),t(d,{label:"状态"},{default:o(()=>[t(Ye,{modelValue:a(u).status,"onUpdate:modelValue":e[10]||(e[10]=l=>a(u).status=l)},{default:o(()=>[(m(!0),L(W,null,X(a(Z),l=>(m(),y(Qe,{key:l.value,value:l.value},{default:o(()=>[i(ve(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(d,{label:"菜单权限"},{default:o(()=>[t(x,{modelValue:a($),"onUpdate:modelValue":e[11]||(e[11]=l=>V($)?$.value=l:null),onChange:e[12]||(e[12]=l=>ue(l,"menu"))},{default:o(()=>[i("展开/折叠")]),_:1},8,["modelValue"]),t(x,{modelValue:a(q),"onUpdate:modelValue":e[13]||(e[13]=l=>V(q)?q.value=l:null),onChange:e[14]||(e[14]=l=>de(l,"menu"))},{default:o(()=>[i("全选/全不选")]),_:1},8,["modelValue"]),t(x,{modelValue:a(u).menuCheckStrictly,"onUpdate:modelValue":e[15]||(e[15]=l=>a(u).menuCheckStrictly=l),onChange:e[16]||(e[16]=l=>re(l,"menu"))},{default:o(()=>[i("父子联动")]),_:1},8,["modelValue"]),t(ie,{class:"tree-border",data:a(R),"show-checkbox":"",ref_key:"menuRef",ref:g,"node-key":"id","check-strictly":!a(u).menuCheckStrictly,"empty-text":"加载中，请稍候",props:{label:"label",children:"children"}},null,8,["data","check-strictly"])]),_:1}),t(d,{label:"备注"},{default:o(()=>[t(p,{modelValue:a(u).remark,"onUpdate:modelValue":e[17]||(e[17]=l=>a(u).remark=l),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"]),t(pe,{title:a(N),modelValue:a(w),"onUpdate:modelValue":e[28]||(e[28]=l=>V(w)?w.value=l:null),width:"500px","append-to-body":""},{footer:o(()=>[O("div",il,[t(f,{type:"primary",onClick:Me},{default:o(()=>[i("确 定")]),_:1}),t(f,{onClick:Pe},{default:o(()=>[i("取 消")]),_:1})])]),default:o(()=>[t(G,{model:a(u),"label-width":"80px"},{default:o(()=>[t(d,{label:"角色名称"},{default:o(()=>[t(p,{modelValue:a(u).roleName,"onUpdate:modelValue":e[19]||(e[19]=l=>a(u).roleName=l),disabled:!0},null,8,["modelValue"])]),_:1}),t(d,{label:"权限字符"},{default:o(()=>[t(p,{modelValue:a(u).roleKey,"onUpdate:modelValue":e[20]||(e[20]=l=>a(u).roleKey=l),disabled:!0},null,8,["modelValue"])]),_:1}),t(d,{label:"权限范围"},{default:o(()=>[t(H,{modelValue:a(u).dataScope,"onUpdate:modelValue":e[21]||(e[21]=l=>a(u).dataScope=l),onChange:$e},{default:o(()=>[(m(!0),L(W,null,X(a(ge),l=>(m(),y(z,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),h(t(d,{label:"数据权限"},{default:o(()=>[t(x,{modelValue:a(M),"onUpdate:modelValue":e[22]||(e[22]=l=>V(M)?M.value=l:null),onChange:e[23]||(e[23]=l=>ue(l,"dept"))},{default:o(()=>[i("展开/折叠")]),_:1},8,["modelValue"]),t(x,{modelValue:a(P),"onUpdate:modelValue":e[24]||(e[24]=l=>V(P)?P.value=l:null),onChange:e[25]||(e[25]=l=>de(l,"dept"))},{default:o(()=>[i("全选/全不选")]),_:1},8,["modelValue"]),t(x,{modelValue:a(u).deptCheckStrictly,"onUpdate:modelValue":e[26]||(e[26]=l=>a(u).deptCheckStrictly=l),onChange:e[27]||(e[27]=l=>re(l,"dept"))},{default:o(()=>[i("父子联动")]),_:1},8,["modelValue"]),t(ie,{class:"tree-border",data:a(A),"show-checkbox":"","default-expand-all":"",ref_key:"deptRef",ref:k,"node-key":"id","check-strictly":!a(u).deptCheckStrictly,"empty-text":"加载中，请稍候",props:{label:"label",children:"children"}},null,8,["data","check-strictly"])]),_:1},512),[[ce,a(u).dataScope==2]])]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}});export{hl as default};
