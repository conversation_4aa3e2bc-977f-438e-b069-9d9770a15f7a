import{s as x,_ as ye,M as ge,g as be,r as y,A as we,R as ke,p as Se,d as u,O as J,c as W,o as r,P as b,e as t,Q as X,k as l,f as e,l as q,N as A,q as o,i as c,h as m,t as d,Z as z,j as N,y as Re,z as Ie}from"./index-B3e47hUR.js";function Ce(f){return x({url:"/system/operation/invoiceRecord/list",method:"get",params:f})}function De(f){return x({url:"/system/operation/invoiceRecord/"+f,method:"get"})}function Ve(f){return x({url:"/system/operation/invoiceRecord/"+f,method:"delete"})}function Ne(f){return x({url:"/system/operation/invoiceRecord/reopen/"+f,method:"post"})}function xe(f){return x({url:"/system/operation/invoiceRecord/reverse/"+f,method:"post"})}const B=f=>(Re("data-v-1e673b71"),f=f(),Ie(),f),Ue={class:"app-container"},Ee={class:"amount-text"},Te={key:0,class:"invoice-detail-view"},Pe={class:"card-header"},$e=B(()=>m("span",{class:"header-title"},"发票基本信息",-1)),Me={class:"amount-text"},Fe={class:"tax-text"},qe=B(()=>m("div",{class:"card-header"},[m("span",{class:"header-title"},"发票抬头信息")],-1)),Ae=B(()=>m("div",{class:"card-header"},[m("span",{class:"header-title"},"联系信息")],-1)),ze={class:"dialog-footer"},Be=ge({name:"InvoiceRecord"}),Le=Object.assign(Be,{setup(f){const{proxy:w}=be(),L=y([]),C=y(!1),T=y(!0),U=y(!0),K=y([]),ee=y(!0),Q=y(!0),P=y(0),Y=y(""),k=y([]),G=y(!1),te=we({form:{},queryParams:{pageNum:1,pageSize:10,invoiceNo:null,tradeId:null,invoiceTitleContent:null,status:null}}),{queryParams:s,form:n}=ke(te);function S(){T.value=!0,s.value.params={},k.value!=null&&k.value!=""&&(s.value.params.beginTime=k.value[0],s.value.params.endTime=k.value[1]),Ce(s.value).then(_=>{L.value=_.rows,P.value=_.total,T.value=!1})}function D(){s.value.pageNum=1,S()}function le(){k.value=[],w.resetForm("queryRef"),D()}function ae(_){K.value=_.map(i=>i.id),ee.value=_.length!=1,Q.value=!_.length}function oe(_){O(),G.value=!0;const i=_.id;De(i).then(V=>{n.value=V.data,C.value=!0,Y.value="查看发票详情"})}function j(_){const i=_.id||K.value;w.$modal.confirm('是否确认删除发票记录编号为"'+i+'"的数据项？').then(function(){return Ve(i)}).then(()=>{S(),w.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ne(_){w.$modal.confirm('确认要重开发票"'+_.invoiceNo+'"吗？').then(function(){return Ne(_.id)}).then(()=>{S(),w.$modal.msgSuccess("发票重开成功")}).catch(()=>{})}function ie(_){w.$modal.confirm('确认要冲红发票"'+_.invoiceNo+'"吗？冲红后发票将无法使用！').then(function(){return xe(_.id)}).then(()=>{S(),w.$modal.msgSuccess("发票冲红成功")}).catch(()=>{})}function re(){w.download("system/operation/invoiceRecord/export",{...s.value},`invoice_record_${new Date().getTime()}.xlsx`)}function de(){C.value=!1,O()}function O(){n.value={}}return Se(()=>{S()}),(_,i)=>{const V=u("el-input"),R=u("el-form-item"),E=u("el-option"),se=u("el-select"),ue=u("el-date-picker"),g=u("el-button"),ce=u("el-form"),Z=u("el-col"),pe=u("right-toolbar"),_e=u("el-row"),v=u("el-table-column"),h=u("el-tag"),fe=u("el-table"),me=u("pagination"),p=u("el-descriptions-item"),$=u("el-descriptions"),M=u("el-card"),H=u("el-link"),ve=u("el-dialog"),I=J("hasPermi"),he=J("loading");return r(),W("div",Ue,[b(t(ce,{model:l(s),ref:"queryRef",inline:!0,"label-width":"68px"},{default:e(()=>[t(R,{label:"发票号码",prop:"invoiceNo"},{default:e(()=>[t(V,{modelValue:l(s).invoiceNo,"onUpdate:modelValue":i[0]||(i[0]=a=>l(s).invoiceNo=a),placeholder:"请输入发票号码",clearable:"",onKeyup:q(D,["enter"])},null,8,["modelValue"])]),_:1}),t(R,{label:"交易订单号",prop:"tradeId"},{default:e(()=>[t(V,{modelValue:l(s).tradeId,"onUpdate:modelValue":i[1]||(i[1]=a=>l(s).tradeId=a),placeholder:"请输入交易订单号",clearable:"",onKeyup:q(D,["enter"])},null,8,["modelValue"])]),_:1}),t(R,{label:"发票抬头",prop:"invoiceTitleContent"},{default:e(()=>[t(V,{modelValue:l(s).invoiceTitleContent,"onUpdate:modelValue":i[2]||(i[2]=a=>l(s).invoiceTitleContent=a),placeholder:"请输入发票抬头",clearable:"",onKeyup:q(D,["enter"])},null,8,["modelValue"])]),_:1}),t(R,{label:"发票状态",prop:"status"},{default:e(()=>[t(se,{modelValue:l(s).status,"onUpdate:modelValue":i[3]||(i[3]=a=>l(s).status=a),placeholder:"请选择发票状态",clearable:""},{default:e(()=>[t(E,{label:"已开具",value:"ISSUED"}),t(E,{label:"已冲红",value:"REVERSED"}),t(E,{label:"开票中",value:"ISSUING"}),t(E,{label:"开票失败",value:"FAILED"})]),_:1},8,["modelValue"])]),_:1}),t(R,{label:"创建时间",style:{width:"308px"}},{default:e(()=>[t(ue,{modelValue:l(k),"onUpdate:modelValue":i[4]||(i[4]=a=>A(k)?k.value=a:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),t(R,null,{default:e(()=>[t(g,{type:"primary",icon:"Search",onClick:D},{default:e(()=>[o("搜索")]),_:1}),t(g,{icon:"Refresh",onClick:le},{default:e(()=>[o("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[X,l(U)]]),t(_e,{gutter:10,class:"mb8"},{default:e(()=>[t(Z,{span:1.5},{default:e(()=>[b((r(),c(g,{type:"danger",plain:"",icon:"Delete",disabled:l(Q),onClick:j},{default:e(()=>[o("删除")]),_:1},8,["disabled"])),[[I,["operation:invoiceRecord:remove"]]])]),_:1}),t(Z,{span:1.5},{default:e(()=>[b((r(),c(g,{type:"warning",plain:"",icon:"Download",onClick:re},{default:e(()=>[o("导出")]),_:1})),[[I,["operation:invoiceRecord:export"]]])]),_:1}),t(pe,{showSearch:l(U),"onUpdate:showSearch":i[5]||(i[5]=a=>A(U)?U.value=a:null),onQueryTable:S},null,8,["showSearch"])]),_:1}),b((r(),c(fe,{data:l(L),onSelectionChange:ae},{default:e(()=>[t(v,{type:"selection",width:"55",align:"center"}),t(v,{label:"发票号码",align:"center",prop:"invoiceNo",width:"180"}),t(v,{label:"发票代码",align:"center",prop:"invoiceCode",width:"120"}),t(v,{label:"交易订单号",align:"center",prop:"tradeId",width:"200"}),t(v,{label:"发票抬头",align:"center",prop:"invoiceTitleContent",width:"150","show-overflow-tooltip":""}),t(v,{label:"场库名称",align:"center",prop:"warehouseName",width:"120"}),t(v,{label:"开票金额",align:"center",prop:"totalMoney",width:"100"},{default:e(a=>[m("span",Ee,"¥"+d(a.row.totalMoney||"0.00"),1)]),_:1}),t(v,{label:"发票状态",align:"center",prop:"status",width:"100"},{default:e(a=>[a.row.status==="ISSUED"?(r(),c(h,{key:0,type:"success"},{default:e(()=>[o("已开具")]),_:1})):a.row.status==="REVERSED"?(r(),c(h,{key:1,type:"danger"},{default:e(()=>[o("已冲红")]),_:1})):a.row.status==="ISSUING"?(r(),c(h,{key:2,type:"warning"},{default:e(()=>[o("开票中")]),_:1})):a.row.status==="FAILED"?(r(),c(h,{key:3,type:"danger"},{default:e(()=>[o("开票失败")]),_:1})):(r(),c(h,{key:4,type:"info"},{default:e(()=>[o(d(a.row.status||"未知"),1)]),_:2},1024))]),_:1}),t(v,{label:"开票日期",align:"center",prop:"issueDate",width:"160"},{default:e(a=>[m("span",null,d(l(z)(a.row.issueDate,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),t(v,{label:"创建时间",align:"center",prop:"createTime",width:"160"},{default:e(a=>[m("span",null,d(l(z)(a.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),t(v,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"200"},{default:e(a=>[b((r(),c(g,{link:"",type:"primary",icon:"View",onClick:F=>oe(a.row)},{default:e(()=>[o("查看")]),_:2},1032,["onClick"])),[[I,["operation:invoiceRecord:query"]]]),a.row.status==="ISSUED"?b((r(),c(g,{key:0,link:"",type:"warning",icon:"RefreshRight",onClick:F=>ne(a.row)},{default:e(()=>[o("重开")]),_:2},1032,["onClick"])),[[I,["operation:invoiceRecord:reopen"]]]):N("",!0),a.row.status==="ISSUED"?b((r(),c(g,{key:1,link:"",type:"danger",icon:"Close",onClick:F=>ie(a.row)},{default:e(()=>[o("冲红")]),_:2},1032,["onClick"])),[[I,["operation:invoiceRecord:reverse"]]]):N("",!0),b((r(),c(g,{link:"",type:"primary",icon:"Delete",onClick:F=>j(a.row)},{default:e(()=>[o("删除")]),_:2},1032,["onClick"])),[[I,["operation:invoiceRecord:remove"]]])]),_:1})]),_:1},8,["data"])),[[he,l(T)]]),b(t(me,{total:l(P),page:l(s).pageNum,"onUpdate:page":i[6]||(i[6]=a=>l(s).pageNum=a),limit:l(s).pageSize,"onUpdate:limit":i[7]||(i[7]=a=>l(s).pageSize=a),onPagination:S},null,8,["total","page","limit"]),[[X,l(P)>0]]),t(ve,{title:l(Y),modelValue:l(C),"onUpdate:modelValue":i[8]||(i[8]=a=>A(C)?C.value=a:null),width:"900px","append-to-body":""},{footer:e(()=>[m("div",ze,[t(g,{onClick:de},{default:e(()=>[o("关 闭")]),_:1})])]),default:e(()=>[l(G)?(r(),W("div",Te,[t(M,{class:"detail-card",shadow:"never"},{header:e(()=>[m("div",Pe,[$e,t(h,{type:"primary",size:"small"},{default:e(()=>[o(d(l(n).invoiceNo||"--"),1)]),_:1})])]),default:e(()=>[t($,{column:2,border:""},{default:e(()=>[t(p,{label:"发票号码"},{default:e(()=>[o(d(l(n).invoiceNo||"--"),1)]),_:1}),t(p,{label:"发票代码"},{default:e(()=>[o(d(l(n).invoiceCode||"--"),1)]),_:1}),t(p,{label:"交易订单号"},{default:e(()=>[o(d(l(n).tradeId||"--"),1)]),_:1}),t(p,{label:"发票状态"},{default:e(()=>[l(n).status==="ISSUED"?(r(),c(h,{key:0,type:"success"},{default:e(()=>[o("已开具")]),_:1})):l(n).status==="REVERSED"?(r(),c(h,{key:1,type:"danger"},{default:e(()=>[o("已冲红")]),_:1})):l(n).status==="ISSUING"?(r(),c(h,{key:2,type:"warning"},{default:e(()=>[o("开票中")]),_:1})):l(n).status==="FAILED"?(r(),c(h,{key:3,type:"danger"},{default:e(()=>[o("开票失败")]),_:1})):(r(),c(h,{key:4,type:"info"},{default:e(()=>[o(d(l(n).status||"未知"),1)]),_:1}))]),_:1}),t(p,{label:"开票金额"},{default:e(()=>[m("span",Me,"¥"+d(l(n).totalMoney||"0.00"),1)]),_:1}),t(p,{label:"税额"},{default:e(()=>[m("span",Fe,"¥"+d(l(n).totalTax||"0.00"),1)]),_:1}),t(p,{label:"开票日期"},{default:e(()=>[o(d(l(z)(l(n).issueDate,"{y}-{m}-{d} {h}:{i}:{s}")||"--"),1)]),_:1}),t(p,{label:"场库名称"},{default:e(()=>[o(d(l(n).warehouseName||"--"),1)]),_:1})]),_:1})]),_:1}),t(M,{class:"detail-card",shadow:"never"},{header:e(()=>[qe]),default:e(()=>[t($,{column:2,border:""},{default:e(()=>[t(p,{label:"发票抬头"},{default:e(()=>[o(d(l(n).invoiceTitleContent||"--"),1)]),_:1}),t(p,{label:"纳税人识别号"},{default:e(()=>[o(d(l(n).unitDutyParagraph||"--"),1)]),_:1}),t(p,{label:"注册地址"},{default:e(()=>[o(d(l(n).registerAddress||"--"),1)]),_:1}),t(p,{label:"注册电话"},{default:e(()=>[o(d(l(n).registerPhone||"--"),1)]),_:1}),t(p,{label:"开户银行"},{default:e(()=>[o(d(l(n).depositBank||"--"),1)]),_:1}),t(p,{label:"银行账号"},{default:e(()=>[o(d(l(n).bankAccount||"--"),1)]),_:1})]),_:1})]),_:1}),t(M,{class:"detail-card",shadow:"never"},{header:e(()=>[Ae]),default:e(()=>[t($,{column:2,border:""},{default:e(()=>[t(p,{label:"通知手机号"},{default:e(()=>[o(d(l(n).notifyMobileNo||"--"),1)]),_:1}),t(p,{label:"通知邮箱"},{default:e(()=>[o(d(l(n).notifyEmail||"--"),1)]),_:1}),l(n).pdfUrl?(r(),c(p,{key:0,label:"PDF地址"},{default:e(()=>[t(H,{href:l(n).pdfUrl,target:"_blank",type:"primary"},{default:e(()=>[o("查看PDF")]),_:1},8,["href"])]),_:1})):N("",!0),l(n).pdfPreviewUrl?(r(),c(p,{key:1,label:"PDF预览地址"},{default:e(()=>[t(H,{href:l(n).pdfPreviewUrl,target:"_blank",type:"primary"},{default:e(()=>[o("预览PDF")]),_:1},8,["href"])]),_:1})):N("",!0)]),_:1})]),_:1})])):N("",!0)]),_:1},8,["title","modelValue"])])}}}),Qe=ye(Le,[["__scopeId","data-v-1e673b71"]]);export{Qe as default};
