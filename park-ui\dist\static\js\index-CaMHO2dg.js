import{s as M,_ as we,M as ye,g as ve,r as d,A as ke,R as Te,p as Se,d as r,O as Q,c as v,o as s,P as k,e as a,Q as W,k as o,f as n,l as Ve,J as O,K as Y,i as f,q as _,N as A,t as T,h as B,j as Ne,a3 as De}from"./index-B3e47hUR.js";import{o as Ie}from"./operator-b9e2JnZC.js";import{o as Ue,a as xe}from"./warehouse-I5QKWbAT.js";function Ce(y){return M({url:"/system/owner/blacklist/list",method:"get",params:y})}function Oe(y){return M({url:"/system/owner/blacklist",method:"post",data:y})}function Ye(y){return M({url:"/system/owner/blacklist/"+y,method:"delete"})}const Be={class:"app-container"},Me={key:1},Pe={key:0,class:"time-display"},Re={class:"dialog-footer"},qe=ye({name:"Blacklist"}),$e=Object.assign(qe,{setup(y){const{proxy:c}=ve(),P=d([]),R=d([]),S=d([]),q=d([]),b=d(!1),N=d(!0),V=d(!0),K=d([]),L=d(!0),H=d(!0),D=d(0),$=d(""),J=ke({form:{},queryParams:{pageNum:1,pageSize:10,plateNo:null,warehouseId:null},rules:{warehouseId:[{required:!0,message:"场库不能为空",trigger:"change"}],plateNo:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],endTime:[{required:!0,message:"结束时间不能为空",trigger:"blur"}]}}),{queryParams:u,form:i,rules:G}=Te(J);function h(){N.value=!0,Ce(u.value).then(t=>{P.value=t.rows,D.value=t.total,N.value=!1})}function F(){Ie().then(t=>{R.value=t.data})}function X(){Ue().then(t=>{q.value=t.data})}function Z(t){i.value.warehouseId=null,S.value=[],t&&xe(t).then(l=>{S.value=l.data})}function ee(){b.value=!1,j()}function j(){const t=new Date,l=t.getFullYear()+"-"+String(t.getMonth()+1).padStart(2,"0")+"-"+String(t.getDate()).padStart(2,"0")+" 00:00:00",m=t.getFullYear()+"-"+String(t.getMonth()+1).padStart(2,"0")+"-"+String(t.getDate()).padStart(2,"0");i.value={id:null,selectedOperatorId:null,plateNo:null,warehouseId:null,beginTime:l,beginTimeDisplay:m,endTime:null,imgUrl:null},S.value=[],c.resetForm("blacklistRef")}function I(){u.value.pageNum=1,h()}function le(){c.resetForm("queryRef"),Object.assign(u.value,{pageNum:1,pageSize:10,plateNo:null,warehouseId:null}),I()}function te(t){K.value=t.map(l=>l.id),L.value=t.length!=1,H.value=!t.length}function ae(){j(),F(),b.value=!0,$.value="添加黑名单管理"}function oe(){c.$refs.blacklistRef.validate(t=>{if(t){let l={...i.value};delete l.selectedOperatorId,delete l.beginTimeDisplay;const m=new Date,p=m.getFullYear()+"-"+String(m.getMonth()+1).padStart(2,"0")+"-"+String(m.getDate()).padStart(2,"0")+" 00:00:00";l.beginTime=p,l.endTime&&(l.endTime=l.endTime+" 23:59:59"),l.id!=null?updateBlacklist(l).then(()=>{c.$modal.msgSuccess("修改成功"),b.value=!1,h()}):Oe(l).then(()=>{c.$modal.msgSuccess("新增成功"),b.value=!1,h()})}})}function ne(t){if(!t||!t.id){c.$modal.msgError("请选择要删除的记录");return}c.$modal.confirm('是否确认删除车牌号为"'+t.plateNo+'"的黑名单记录？').then(function(){return Ye(t.id)}).then(()=>{h(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ie(){c.download("system/owner/blacklist/export",{...u.value},`blacklist_${new Date().getTime()}.xlsx`)}function re(t){const l=new Date;return l.setHours(0,0,0,0),t.getTime()<l.getTime()}function se(t){return t?t.length===8?"success":"primary":"info"}function de(t){return t?t.length===8?"#d4edda":"#cce7ff":"#909399"}return Se(()=>{h(),F(),X()}),(t,l)=>{const m=r("el-input"),p=r("el-form-item"),U=r("el-option"),x=r("el-select"),w=r("el-button"),z=r("el-form"),E=r("el-col"),ue=r("right-toolbar"),pe=r("el-row"),g=r("el-table-column"),ce=r("el-tag"),me=r("el-image"),ge=r("el-table"),fe=r("pagination"),_e=r("el-date-picker"),be=r("el-dialog"),C=Q("hasPermi"),he=Q("loading");return s(),v("div",Be,[k(a(z,{model:o(u),ref:"queryRef",inline:!0,"label-width":"68px"},{default:n(()=>[a(p,{label:"车牌号",prop:"plateNo"},{default:n(()=>[a(m,{modelValue:o(u).plateNo,"onUpdate:modelValue":l[0]||(l[0]=e=>o(u).plateNo=e),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:Ve(I,["enter"])},null,8,["modelValue"])]),_:1}),a(p,{label:"场库",prop:"warehouseId"},{default:n(()=>[a(x,{modelValue:o(u).warehouseId,"onUpdate:modelValue":l[1]||(l[1]=e=>o(u).warehouseId=e),placeholder:"请选择场库",clearable:"",style:{width:"200px"}},{default:n(()=>[(s(!0),v(O,null,Y(o(q),e=>(s(),f(U,{key:e.id,label:e.warehouseName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,null,{default:n(()=>[a(w,{type:"primary",icon:"Search",onClick:I},{default:n(()=>[_("搜索")]),_:1}),a(w,{icon:"Refresh",onClick:le},{default:n(()=>[_("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[W,o(V)]]),a(pe,{gutter:10,class:"mb8"},{default:n(()=>[a(E,{span:1.5},{default:n(()=>[k((s(),f(w,{type:"primary",plain:"",icon:"Plus",onClick:ae},{default:n(()=>[_("新增")]),_:1})),[[C,["owner:blacklist:add"]]])]),_:1}),a(E,{span:1.5},{default:n(()=>[k((s(),f(w,{type:"warning",plain:"",icon:"Download",onClick:ie},{default:n(()=>[_("导出")]),_:1})),[[C,["owner:blacklist:export"]]])]),_:1}),a(ue,{showSearch:o(V),"onUpdate:showSearch":l[2]||(l[2]=e=>A(V)?V.value=e:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),k((s(),f(ge,{data:o(P),onSelectionChange:te},{default:n(()=>[a(g,{type:"selection",width:"55",align:"center"}),a(g,{label:"运营商",align:"center",prop:"operatorName"}),a(g,{label:"场库名称",align:"center",prop:"warehouseName"}),a(g,{label:"车牌号",align:"center",prop:"plateNo"},{default:n(e=>[a(ce,{type:se(e.row.plateNo),color:de(e.row.plateNo),effect:"plain"},{default:n(()=>[_(T(e.row.plateNo),1)]),_:2},1032,["type","color"])]),_:1}),a(g,{label:"开始时间",align:"center",prop:"beginTime",width:"180"},{default:n(e=>[B("span",null,T(t.parseTime(e.row.beginTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),a(g,{label:"过期时间",align:"center",prop:"endTime",width:"180"},{default:n(e=>[B("span",null,T(t.parseTime(e.row.endTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),a(g,{label:"证据图片",align:"center",prop:"imgUrl",width:"100"},{default:n(e=>[e.row.imgUrl?(s(),f(me,{key:0,style:{width:"50px",height:"50px"},src:e.row.imgUrl,"preview-src-list":[e.row.imgUrl],fit:"cover"},null,8,["src","preview-src-list"])):(s(),v("span",Me,"-"))]),_:1}),a(g,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(e=>[k((s(),f(w,{link:"",type:"primary",icon:"Delete",onClick:Fe=>ne(e.row)},{default:n(()=>[_("删除")]),_:2},1032,["onClick"])),[[C,["owner:blacklist:remove"]]])]),_:1})]),_:1},8,["data"])),[[he,o(N)]]),k(a(fe,{total:o(D),page:o(u).pageNum,limit:o(u).pageSize,onPagination:h},null,8,["total","page","limit"]),[[W,o(D)>0]]),a(be,{title:o($),modelValue:o(b),"onUpdate:modelValue":l[9]||(l[9]=e=>A(b)?b.value=e:null),width:"500px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:n(()=>[B("div",Re,[a(w,{type:"primary",onClick:oe},{default:n(()=>[_("确 定")]),_:1}),a(w,{onClick:ee},{default:n(()=>[_("取 消")]),_:1})])]),default:n(()=>[a(z,{ref:"blacklistRef",model:o(i),rules:o(G),"label-width":"80px"},{default:n(()=>[a(p,{label:"运营商",prop:"selectedOperatorId"},{default:n(()=>[a(x,{modelValue:o(i).selectedOperatorId,"onUpdate:modelValue":l[3]||(l[3]=e=>o(i).selectedOperatorId=e),placeholder:"请选择运营商",clearable:"",style:{width:"100%"},onChange:Z},{default:n(()=>[(s(!0),v(O,null,Y(o(R),e=>(s(),f(U,{key:e.id,label:e.companyName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"场库",prop:"warehouseId"},{default:n(()=>[a(x,{modelValue:o(i).warehouseId,"onUpdate:modelValue":l[4]||(l[4]=e=>o(i).warehouseId=e),placeholder:"请选择场库",clearable:"",style:{width:"100%"},disabled:!o(i).selectedOperatorId},{default:n(()=>[(s(!0),v(O,null,Y(o(S),e=>(s(),f(U,{key:e.id,label:e.warehouseName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),a(p,{label:"车牌号",prop:"plateNo"},{default:n(()=>[a(m,{modelValue:o(i).plateNo,"onUpdate:modelValue":l[5]||(l[5]=e=>o(i).plateNo=e),placeholder:"请输入车牌号"},null,8,["modelValue"])]),_:1}),a(p,{label:"开始时间",prop:"beginTime"},{default:n(()=>[a(m,{modelValue:o(i).beginTimeDisplay,"onUpdate:modelValue":l[6]||(l[6]=e=>o(i).beginTimeDisplay=e),placeholder:"开始时间",disabled:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(p,{label:"结束时间",prop:"endTime"},{default:n(()=>[a(_e,{modelValue:o(i).endTime,"onUpdate:modelValue":l[7]||(l[7]=e=>o(i).endTime=e),type:"date",placeholder:"选择结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":re,style:{width:"100%"}},null,8,["modelValue"]),o(i).beginTimeDisplay&&o(i).endTime?(s(),v("div",Pe," 时间："+T(o(i).beginTimeDisplay)+" 00:00:00 - "+T(o(i).endTime)+" 23:59:59 ",1)):Ne("",!0)]),_:1}),a(p,{label:"证据图片",prop:"imgUrl"},{default:n(()=>[a(De,{modelValue:o(i).imgUrl,"onUpdate:modelValue":l[8]||(l[8]=e=>o(i).imgUrl=e),limit:1,action:"/file/upload/path",data:{path:"black"},"file-size":5,"file-type":["jpg","jpeg","png"]},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Qe=we($e,[["__scopeId","data-v-cb494ce5"]]);export{Qe as default};
