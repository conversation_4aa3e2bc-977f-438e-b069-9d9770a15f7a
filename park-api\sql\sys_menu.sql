/*
 Navicat MySQL Data Transfer

 Source Server         : parkingnew
 Source Server Type    : MySQL
 Source Server Version : 80400
 Source Host           : localhost:3306
 Source Schema         : parknew

 Target Server Type    : MySQL
 Target Server Version : 80400
 File Encoding         : 65001

 Date: 30/07/2025 11:02:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` bigint UNSIGNED NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint UNSIGNED NULL DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2179 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 1, '2025-06-04 11:01:40', 1, '2025-06-04 13:14:24', '系统监控目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '在线用户菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'system/operlog/index', '', '', 1, 0, 'C', '0', '0', 'system:operlog:list', 'form', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'system/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'system:logininfor:list', 'logininfor', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:operlog:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:operlog:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:operlog:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:remove', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:export', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:unlock', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 1, '2025-06-04 11:01:40', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2000, '广告配置管理', 2006, 1, 'advertConfig', 'operation/advertConfig/index', NULL, '', 1, 0, 'C', '0', '0', 'system:advertConfig:list', 'guide', 1, '2025-06-06 13:51:35', 1, '2025-07-02 23:26:19', '广告配置信息菜单');
INSERT INTO `sys_menu` VALUES (2001, '广告配置查询', 2000, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:advertConfig:query', '#', 1, '2025-06-06 13:51:55', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2002, '广告配置新增', 2000, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:advertConfig:add', '#', 1, '2025-06-06 13:51:55', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2003, '广告配置修改', 2000, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:advertConfig:edit', '#', 1, '2025-06-06 13:51:55', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2004, '广告配置删除', 2000, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:advertConfig:remove', '#', 1, '2025-06-06 13:51:55', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2005, '广告配置导出', 2000, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:advertConfig:export', '#', 1, '2025-06-06 13:51:55', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2006, '运营管理', 0, 4, 'operation', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'chart', 1, '2025-06-06 14:10:37', 1, '2025-07-24 21:15:37', '运营管理目录');
INSERT INTO `sys_menu` VALUES (2007, '平台管理', 0, 3, 'platform', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'table', 1, '2025-06-09 12:37:01', 1, '2025-07-18 15:12:27', '平台管理目录');
INSERT INTO `sys_menu` VALUES (2008, '运营商管理', 2007, 1, 'operator', 'platform/operator/index', NULL, '', 1, 0, 'C', '0', '0', 'platform:operator:list', 'peoples', 1, '2025-06-09 12:37:07', 1, '2025-07-02 23:26:19', '运营商管理菜单');
INSERT INTO `sys_menu` VALUES (2009, '场库管理', 2007, 2, 'warehouse', 'platform/warehouse/index', NULL, '', 1, 0, 'C', '0', '0', 'platform:warehouse:list', 'build', 1, '2025-06-09 12:37:13', 1, '2025-07-02 23:26:19', '场库管理菜单');
INSERT INTO `sys_menu` VALUES (2010, '场库负责人', 2007, 3, 'warehouseManager', 'platform/warehouseManager/index', NULL, '', 1, 0, 'C', '0', '0', 'platform:warehouseManager:list', 'user', 1, '2025-06-09 12:37:20', 1, '2025-07-18 23:24:09', '场库管理人员菜单');
INSERT INTO `sys_menu` VALUES (2011, '运营商查询', 2008, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:operator:query', '#', 1, '2025-06-09 12:37:30', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2012, '运营商新增', 2008, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:operator:add', '#', 1, '2025-06-09 12:37:30', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2013, '运营商修改', 2008, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:operator:edit', '#', 1, '2025-06-09 12:37:30', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2014, '运营商删除', 2008, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:operator:remove', '#', 1, '2025-06-09 12:37:30', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2015, '运营商导出', 2008, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:operator:export', '#', 1, '2025-06-09 12:37:30', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2016, '场库查询', 2009, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouse:query', '#', 1, '2025-06-09 12:37:37', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2017, '场库新增', 2009, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouse:add', '#', 1, '2025-06-09 12:37:37', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2018, '场库修改', 2009, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouse:edit', '#', 1, '2025-06-09 12:37:37', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2019, '场库删除', 2009, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouse:remove', '#', 1, '2025-06-09 12:37:37', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2020, '场库导出', 2009, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouse:export', '#', 1, '2025-06-09 12:37:37', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2021, '管理人员查询', 2010, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouseManager:query', '#', 1, '2025-06-09 12:37:47', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2022, '管理人员新增', 2010, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouseManager:add', '#', 1, '2025-06-09 12:37:47', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2023, '管理人员修改', 2010, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouseManager:edit', '#', 1, '2025-06-09 12:37:47', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2024, '管理人员删除', 2010, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouseManager:remove', '#', 1, '2025-06-09 12:37:47', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2025, '管理人员导出', 2010, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:warehouseManager:export', '#', 1, '2025-06-09 12:37:47', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2026, '会员管理', 0, 5, 'vip', '', '', '', 1, 0, 'M', '0', '0', '', 'money', 1, '2025-06-09 14:45:53', 1, '2025-06-27 10:06:06', '会员管理目录');
INSERT INTO `sys_menu` VALUES (2030, '会员套餐管理', 2026, 1, 'vip-package', 'vip/package/index', '', '', 1, 0, 'C', '0', '0', 'vip:package:list', 'shopping', 1, '2025-06-09 14:46:17', 1, '2025-07-20 15:57:19', '会员套餐配置菜单');
INSERT INTO `sys_menu` VALUES (2031, '会员信息管理', 2026, 2, 'vip-member', 'vip/member/index', '', '', 1, 0, 'C', '0', '0', 'vip:member:list', 'user', 1, '2025-06-09 14:46:24', 1, '2025-07-02 23:26:19', '会员信息管理菜单');
INSERT INTO `sys_menu` VALUES (2032, '会员订单管理', 2026, 3, 'transaction', 'vip/transaction/index', '', '', 1, 0, 'C', '0', '0', 'vip:transaction:list', 'money', 1, '2025-06-09 14:46:42', 1, '2025-07-20 15:57:10', '会员交易记录菜单');
INSERT INTO `sys_menu` VALUES (2033, '会员套餐查询', 2030, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:package:query', '#', 1, '2025-06-09 14:46:59', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2034, '会员套餐新增', 2030, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:package:add', '#', 1, '2025-06-09 14:46:59', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2035, '会员套餐修改', 2030, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:package:edit', '#', 1, '2025-06-09 14:46:59', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2036, '会员套餐删除', 2030, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:package:remove', '#', 1, '2025-06-09 14:46:59', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2037, '会员套餐导出', 2030, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:package:export', '#', 1, '2025-06-09 14:46:59', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2038, '会员信息查询', 2031, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:member:query', '#', 1, '2025-06-09 14:47:07', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2039, '会员信息新增', 2031, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:member:add', '#', 1, '2025-06-09 14:47:07', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2040, '会员信息修改', 2031, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:member:edit', '#', 1, '2025-06-09 14:47:07', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2041, '会员信息删除', 2031, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:member:remove', '#', 1, '2025-06-09 14:47:07', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2042, '会员信息导出', 2031, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:member:export', '#', 1, '2025-06-09 14:47:07', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2043, '交易记录查询', 2032, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:query', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2044, '交易记录新增', 2032, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:add', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2045, '交易记录修改', 2032, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:edit', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2046, '交易记录删除', 2032, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:remove', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2047, '交易记录导出', 2032, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:export', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2048, '交易退款处理', 2032, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'vip:transaction:refund', '#', 1, '2025-06-09 14:47:16', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2098, '临停管理', 0, 4, 'order', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'shopping', 1, '2025-06-19 12:18:27', 1, '2025-07-23 13:50:06', '订单管理目录');
INSERT INTO `sys_menu` VALUES (2099, '停车订单管理', 2098, 1, 'parkingOrder', 'order/parkingOrder/index', NULL, 'ParkingOrder', 1, 0, 'C', '0', '0', 'order:parkingOrder:list', 'parking-order', 1, '2025-06-19 12:18:40', 1, '2025-07-02 23:26:19', '停车订单管理菜单');
INSERT INTO `sys_menu` VALUES (2100, '停车订单查询', 2099, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:parkingOrder:query', '#', 1, '2025-06-19 12:18:56', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2101, '停车订单新增', 2099, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:parkingOrder:add', '#', 1, '2025-06-19 12:18:56', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2102, '停车订单修改', 2099, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:parkingOrder:edit', '#', 1, '2025-06-19 12:18:56', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2103, '停车订单删除', 2099, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:parkingOrder:remove', '#', 1, '2025-06-19 12:18:56', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2104, '停车订单导出', 2099, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:parkingOrder:export', '#', 1, '2025-06-19 12:18:56', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2105, '异常订单管理', 2098, 2, 'exceptionOrder', 'order/exceptionOrder/index', NULL, 'ExceptionOrder', 1, 0, 'C', '0', '0', 'order:exceptionOrder:list', 'exception-order', 1, '2025-06-19 12:19:05', 1, '2025-07-02 23:26:19', '异常订单管理菜单');
INSERT INTO `sys_menu` VALUES (2106, '异常订单查询', 2105, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'order:exceptionOrder:query', '#', 1, '2025-06-19 13:37:58', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2107, '异常订单新增', 2105, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'order:exceptionOrder:add', '#', 1, '2025-06-19 13:37:58', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2108, '异常订单修改', 2105, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'order:exceptionOrder:edit', '#', 1, '2025-06-19 13:37:58', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2109, '异常订单删除', 2105, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'order:exceptionOrder:remove', '#', 1, '2025-06-19 13:37:58', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2110, '异常订单导出', 2105, 5, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'order:exceptionOrder:export', '#', 1, '2025-06-19 13:37:58', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2111, '异常订单处理', 2105, 6, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'order:exceptionOrder:handle', '#', 1, '2025-06-19 13:37:58', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2112, '银联配置管理', 2007, 4, 'unionPayConfig', 'platform/unionPayConfig/index', NULL, '', 1, 0, 'C', '0', '0', 'platform:unionPayConfig:list', 'money', 1, '2025-06-20 14:53:28', 1, '2025-07-18 16:29:19', '银联配置管理菜单');
INSERT INTO `sys_menu` VALUES (2113, '银联配置查询', 2112, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:unionPayConfig:query', '#', 1, '2025-06-20 14:53:50', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2114, '银联配置新增', 2112, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:unionPayConfig:add', '#', 1, '2025-06-20 14:53:50', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2115, '银联配置修改', 2112, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:unionPayConfig:edit', '#', 1, '2025-06-20 14:53:50', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2116, '银联配置删除', 2112, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:unionPayConfig:remove', '#', 1, '2025-06-20 14:53:50', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2117, '银联配置导出', 2112, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'platform:unionPayConfig:export', '#', 1, '2025-06-20 14:53:50', 1, '2025-07-02 23:26:19', '');
INSERT INTO `sys_menu` VALUES (2124, '特殊会员管理', 2026, 4, 'special-user', 'vip/specialUser/index', NULL, '', 1, 0, 'C', '0', '0', 'special:user:list', 'user', 1, '2025-07-03 08:10:57', NULL, '2025-07-20 15:56:59', '特殊会员菜单');
INSERT INTO `sys_menu` VALUES (2125, '特殊会员查询', 2124, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'special:user:query', '#', 1, '2025-07-03 08:11:12', NULL, '2025-07-03 08:11:12', '');
INSERT INTO `sys_menu` VALUES (2126, '特殊会员新增', 2124, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'special:user:add', '#', 1, '2025-07-03 08:11:12', NULL, '2025-07-03 08:11:12', '');
INSERT INTO `sys_menu` VALUES (2127, '特殊会员修改', 2124, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'special:user:edit', '#', 1, '2025-07-03 08:11:12', NULL, '2025-07-03 08:11:12', '');
INSERT INTO `sys_menu` VALUES (2128, '特殊会员删除', 2124, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'special:user:remove', '#', 1, '2025-07-03 08:11:12', NULL, '2025-07-03 08:11:12', '');
INSERT INTO `sys_menu` VALUES (2129, '特殊会员导出', 2124, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'special:user:export', '#', 1, '2025-07-03 08:11:12', NULL, '2025-07-03 08:11:12', '');
INSERT INTO `sys_menu` VALUES (2130, '车主管理', 0, 5, 'owner', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'user', 1, '2025-07-04 10:19:37', NULL, '2025-07-04 10:19:37', '车主管理目录');
INSERT INTO `sys_menu` VALUES (2131, '小程序用户', 2130, 1, 'wxuser', 'owner/wxuser/index', NULL, '', 1, 0, 'C', '0', '0', 'owner:wxuser:list', 'peoples', 1, '2025-07-04 10:19:48', NULL, '2025-07-04 10:19:48', '小程序用户菜单');
INSERT INTO `sys_menu` VALUES (2132, '小程序用户查询', 2131, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:query', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2133, '小程序用户新增', 2131, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:add', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2134, '小程序用户修改', 2131, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:edit', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2135, '小程序用户删除', 2131, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:remove', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2136, '小程序用户导出', 2131, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:export', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2137, '查询用户车辆', 2131, 6, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:wxuser:car', '#', 1, '2025-07-04 10:20:02', NULL, '2025-07-04 10:20:02', '');
INSERT INTO `sys_menu` VALUES (2138, '白名单管理', 2130, 2, 'whitelist', 'owner/whitelist/index', '', '', 1, 0, 'C', '0', '0', 'owner:whitelist:list', 'education', 1, '2025-07-08 15:22:59', 1, '2025-07-21 15:55:47', '白名单管理菜单');
INSERT INTO `sys_menu` VALUES (2139, '白名单查询', 2138, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'owner:whitelist:query', '#', 1, '2025-07-08 15:23:24', 1, '2025-07-08 15:23:24', '');
INSERT INTO `sys_menu` VALUES (2140, '白名单新增', 2138, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'owner:whitelist:add', '#', 1, '2025-07-08 15:23:24', 1, '2025-07-08 15:23:24', '');
INSERT INTO `sys_menu` VALUES (2141, '白名单修改', 2138, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'owner:whitelist:edit', '#', 1, '2025-07-08 15:23:24', 1, '2025-07-08 15:23:24', '');
INSERT INTO `sys_menu` VALUES (2142, '白名单删除', 2138, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'owner:whitelist:remove', '#', 1, '2025-07-08 15:23:24', 1, '2025-07-08 15:23:24', '');
INSERT INTO `sys_menu` VALUES (2143, '白名单导出', 2138, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'owner:whitelist:export', '#', 1, '2025-07-08 15:23:24', 1, '2025-07-08 15:23:24', '');
INSERT INTO `sys_menu` VALUES (2144, '黑名单管理', 2130, 3, 'blacklist', 'owner/blacklist/index', NULL, '', 1, 0, 'C', '0', '0', 'owner:blacklist:list', 'lock', 1, '2025-07-08 20:26:03', NULL, '2025-07-08 20:33:18', '黑名单管理菜单');
INSERT INTO `sys_menu` VALUES (2145, '黑名单查询', 2144, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:blacklist:query', '#', 1, '2025-07-08 20:26:25', NULL, '2025-07-08 20:26:25', '');
INSERT INTO `sys_menu` VALUES (2146, '黑名单新增', 2144, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:blacklist:add', '#', 1, '2025-07-08 20:26:25', NULL, '2025-07-08 20:26:25', '');
INSERT INTO `sys_menu` VALUES (2147, '黑名单修改', 2144, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:blacklist:edit', '#', 1, '2025-07-08 20:26:25', NULL, '2025-07-08 20:26:25', '');
INSERT INTO `sys_menu` VALUES (2148, '黑名单删除', 2144, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:blacklist:remove', '#', 1, '2025-07-08 20:26:25', NULL, '2025-07-08 20:26:25', '');
INSERT INTO `sys_menu` VALUES (2149, '黑名单导出', 2144, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'owner:blacklist:export', '#', 1, '2025-07-08 20:26:25', NULL, '2025-07-08 20:26:25', '');
INSERT INTO `sys_menu` VALUES (2150, '车辆出入场记录管理', 2098, 3, 'gateParkingInfo', 'order/gateParkingInfo/index', NULL, 'GateParkingInfo', 1, 0, 'C', '0', '0', 'order:gateParkingInfo:list', 'parking', 1, '2025-07-11 13:30:30', 1, '2025-07-11 14:44:52', '车辆出入场记录管理菜单');
INSERT INTO `sys_menu` VALUES (2151, '车辆出入场记录查询', 2150, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:gateParkingInfo:query', '#', 1, '2025-07-11 13:30:44', 1, '2025-07-11 13:30:44', '');
INSERT INTO `sys_menu` VALUES (2152, '车辆出入场记录新增', 2150, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:gateParkingInfo:add', '#', 1, '2025-07-11 13:30:50', 1, '2025-07-11 13:30:50', '');
INSERT INTO `sys_menu` VALUES (2153, '车辆出入场记录修改', 2150, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:gateParkingInfo:edit', '#', 1, '2025-07-11 13:30:58', 1, '2025-07-11 13:30:58', '');
INSERT INTO `sys_menu` VALUES (2154, '车辆出入场记录删除', 2150, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:gateParkingInfo:remove', '#', 1, '2025-07-11 13:31:06', 1, '2025-07-11 13:31:06', '');
INSERT INTO `sys_menu` VALUES (2155, '车辆出入场记录导出', 2150, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'order:gateParkingInfo:export', '#', 1, '2025-07-11 13:31:11', 1, '2025-07-11 13:31:11', '');
INSERT INTO `sys_menu` VALUES (2156, '协议管理', 1, 6, 'agreement', 'system/agreement/index', NULL, 'Agreement', 1, 0, 'C', '0', '0', 'system:agreement:list', 'documentation', 1, '2025-07-22 10:02:13', NULL, '2025-07-22 10:02:13', '协议管理菜单');
INSERT INTO `sys_menu` VALUES (2157, '协议查询', 2156, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:agreement:query', '#', 1, '2025-07-22 10:02:30', NULL, '2025-07-22 10:02:30', '');
INSERT INTO `sys_menu` VALUES (2158, '协议新增', 2156, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:agreement:add', '#', 1, '2025-07-22 10:02:30', NULL, '2025-07-22 10:02:30', '');
INSERT INTO `sys_menu` VALUES (2159, '协议修改', 2156, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:agreement:edit', '#', 1, '2025-07-22 10:02:30', NULL, '2025-07-22 10:02:30', '');
INSERT INTO `sys_menu` VALUES (2160, '协议删除', 2156, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:agreement:remove', '#', 1, '2025-07-22 10:02:30', NULL, '2025-07-22 10:02:30', '');
INSERT INTO `sys_menu` VALUES (2161, '协议导出', 2156, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:agreement:export', '#', 1, '2025-07-22 10:02:30', NULL, '2025-07-22 10:02:30', '');
INSERT INTO `sys_menu` VALUES (2162, '退款管理', 0, 5, 'refund', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'money', 1, '2025-07-23 14:14:22', NULL, '2025-07-23 14:14:22', '退款管理目录');
INSERT INTO `sys_menu` VALUES (2163, '退款管理', 2162, 1, 'refund', 'refund/refund/index', NULL, '', 1, 0, 'C', '0', '0', 'refund:refund:list', 'money', 1, '2025-07-23 14:15:17', NULL, '2025-07-23 14:15:17', '退款管理菜单');
INSERT INTO `sys_menu` VALUES (2167, '退款查询', 2163, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'refund:refund:query', '#', 1, '2025-07-23 14:16:22', NULL, '2025-07-23 14:16:22', '');
INSERT INTO `sys_menu` VALUES (2168, '退款查看', 2163, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'refund:refund:list', '#', 1, '2025-07-23 14:16:33', NULL, '2025-07-23 14:16:33', '');
INSERT INTO `sys_menu` VALUES (2169, '退款导出', 2163, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'refund:refund:export', '#', 1, '2025-07-23 14:16:43', NULL, '2025-07-23 14:16:43', '');
INSERT INTO `sys_menu` VALUES (2170, '发票管理', 2006, 2, 'invoiceRecord', 'operation/invoiceRecord/index', NULL, '', 1, 0, 'C', '0', '0', 'operation:invoiceRecord:list', 'money', 1, '2025-07-24 21:23:15', 1, '2025-07-24 21:51:17', '发票记录管理菜单');
INSERT INTO `sys_menu` VALUES (2171, '发票记录查询', 2170, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:invoiceRecord:query', '#', 1, '2025-07-24 21:23:34', 1, '2025-07-24 21:23:34', '');
INSERT INTO `sys_menu` VALUES (2172, '发票记录新增', 2170, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:invoiceRecord:add', '#', 1, '2025-07-24 21:23:34', 1, '2025-07-24 21:23:34', '');
INSERT INTO `sys_menu` VALUES (2173, '发票记录修改', 2170, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:invoiceRecord:edit', '#', 1, '2025-07-24 21:23:34', 1, '2025-07-24 21:23:34', '');
INSERT INTO `sys_menu` VALUES (2174, '发票记录删除', 2170, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:invoiceRecord:remove', '#', 1, '2025-07-24 21:23:34', 1, '2025-07-24 21:23:34', '');
INSERT INTO `sys_menu` VALUES (2175, '发票记录导出', 2170, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:invoiceRecord:export', '#', 1, '2025-07-24 21:23:34', 1, '2025-07-24 21:23:34', '');
INSERT INTO `sys_menu` VALUES (2176, '发票重开', 2170, 6, '', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:invoiceRecord:reopen', '#', 1, '2025-07-24 21:23:34', 1, '2025-07-24 21:23:34', '');
INSERT INTO `sys_menu` VALUES (2177, '发票冲红', 2170, 7, '', '', NULL, '', 1, 0, 'F', '0', '0', 'operation:invoiceRecord:reverse', '#', 1, '2025-07-24 21:23:34', 1, '2025-07-24 21:23:34', '');
INSERT INTO `sys_menu` VALUES (2178, '停车订单退款', 2099, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'order:parkingOrder:refund', '#', 1, '2025-07-30 10:58:51', 1, '2025-07-30 10:58:51', '停车订单退款权限');

SET FOREIGN_KEY_CHECKS = 1;
