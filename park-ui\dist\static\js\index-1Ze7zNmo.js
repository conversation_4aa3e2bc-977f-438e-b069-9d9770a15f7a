import{s as k,M as fe,g as ge,r as g,A as ve,R as _e,d as n,O as F,c as R,o as s,P as _,e as t,Q as he,k as a,f as l,l as ye,J as Q,K as I,i as m,q as u,N as K,j as Ce,h as L,t as O}from"./index-B3e47hUR.js";import{C as J}from"./index-DFSldapw.js";function be(p){return k({url:"/system/advertConfig/list",method:"get",params:p})}function we(p){return k({url:"/system/advertConfig/"+p,method:"get"})}function ke(p){return k({url:"/system/advertConfig",method:"post",data:p})}function Ve(p){return k({url:"/system/advertConfig",method:"put",data:p})}function Ue(p){return k({url:"/system/advertConfig/"+p,method:"delete"})}const Se={class:"app-container"},Te={class:"dialog-footer"},xe=fe({name:"AdvertConfig"}),Pe=Object.assign(xe,{components:{CustomPagination:J}},{setup(p){const{proxy:f}=ge(),{advert_status:S}=f.useDict("advert_status"),q=g([]),v=g(!1),T=g(!0),V=g(!0),x=g([]),A=g(!0),$=g(!0),z=g(0),D=g(""),M=ve({form:{},queryParams:{pageNum:1,pageSize:10,advertTitle:void 0,status:void 0},rules:{advertTitle:[{required:!0,message:"广告标题不能为空",trigger:"blur"}],picUrl:[{required:!0,message:"图片不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}),{queryParams:d,form:r,rules:G}=_e(M);function h(){T.value=!0,be(d.value).then(i=>{q.value=i.rows,z.value=i.total,T.value=!1})}function H(){v.value=!1,N()}function N(){r.value={id:void 0,advertTitle:void 0,url:void 0,picUrl:void 0,status:1,remark:void 0},f.resetForm("advertConfigRef")}function P(){d.value.pageNum=1,h()}function W(){f.resetForm("queryRef"),P()}function X(i){x.value=i.map(o=>o.id),A.value=i.length!=1,$.value=!i.length}function Y(){N(),v.value=!0,D.value="添加广告配置信息"}function j(i){N();const o=i.id||x.value;we(o).then(w=>{r.value=w.data,v.value=!0,D.value="修改广告配置信息"})}function Z(){f.$refs.advertConfigRef.validate(i=>{i&&(r.value.id!=null?Ve(r.value).then(o=>{f.$modal.msgSuccess("修改成功"),v.value=!1,h()}):ke(r.value).then(o=>{f.$modal.msgSuccess("新增成功"),v.value=!1,h()}))})}function B(i){const o=i.id||x.value;f.$modal.confirm('是否确认删除广告配置信息编号为"'+o+'"的数据项？').then(function(){return Ue(o)}).then(()=>{h(),f.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ee(){f.download("system/advertConfig/export",{...d.value},`advertConfig_${new Date().getTime()}.xlsx`)}return h(),(i,o)=>{const w=n("el-input"),y=n("el-form-item"),te=n("el-option"),le=n("el-select"),c=n("el-button"),E=n("el-form"),U=n("el-col"),ae=n("right-toolbar"),oe=n("el-row"),C=n("el-table-column"),ne=n("image-preview"),re=n("dict-tag"),ie=n("el-table"),se=n("image-upload"),de=n("el-radio"),ue=n("el-radio-group"),pe=n("el-dialog"),b=F("hasPermi"),ce=F("loading");return s(),R("div",Se,[_(t(E,{model:a(d),ref:"queryRef",inline:!0},{default:l(()=>[t(y,{label:"广告标题",prop:"advertTitle"},{default:l(()=>[t(w,{modelValue:a(d).advertTitle,"onUpdate:modelValue":o[0]||(o[0]=e=>a(d).advertTitle=e),placeholder:"请输入广告标题",clearable:"",style:{width:"200px"},onKeyup:ye(P,["enter"])},null,8,["modelValue"])]),_:1}),t(y,{label:"状态",prop:"status"},{default:l(()=>[t(le,{modelValue:a(d).status,"onUpdate:modelValue":o[1]||(o[1]=e=>a(d).status=e),placeholder:"广告状态",clearable:"",style:{width:"200px"}},{default:l(()=>[(s(!0),R(Q,null,I(a(S),e=>(s(),m(te,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(y,null,{default:l(()=>[t(c,{type:"primary",icon:"Search",onClick:P},{default:l(()=>[u("搜索")]),_:1}),t(c,{icon:"Refresh",onClick:W},{default:l(()=>[u("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[he,a(V)]]),t(oe,{gutter:10,class:"mb8"},{default:l(()=>[t(U,{span:1.5},{default:l(()=>[_((s(),m(c,{type:"primary",plain:"",icon:"Plus",onClick:Y},{default:l(()=>[u("新增")]),_:1})),[[b,["system:advertConfig:add"]]])]),_:1}),t(U,{span:1.5},{default:l(()=>[_((s(),m(c,{type:"success",plain:"",icon:"Edit",disabled:a(A),onClick:j},{default:l(()=>[u("修改")]),_:1},8,["disabled"])),[[b,["system:advertConfig:edit"]]])]),_:1}),t(U,{span:1.5},{default:l(()=>[_((s(),m(c,{type:"danger",plain:"",icon:"Delete",disabled:a($),onClick:B},{default:l(()=>[u("删除")]),_:1},8,["disabled"])),[[b,["system:advertConfig:remove"]]])]),_:1}),t(U,{span:1.5},{default:l(()=>[_((s(),m(c,{type:"warning",plain:"",icon:"Download",onClick:ee},{default:l(()=>[u("导出")]),_:1})),[[b,["system:advertConfig:export"]]])]),_:1}),t(ae,{showSearch:a(V),"onUpdate:showSearch":o[2]||(o[2]=e=>K(V)?V.value=e:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),_((s(),m(ie,{data:a(q),onSelectionChange:X},{default:l(()=>[t(C,{type:"selection",width:"55",align:"center"}),t(C,{label:"广告标题",align:"center",prop:"advertTitle","show-overflow-tooltip":!0}),t(C,{label:"图片",align:"center",prop:"picUrl",width:"100"},{default:l(e=>[e.row.picUrl?(s(),m(ne,{key:0,src:e.row.picUrl,width:50,height:50},null,8,["src"])):Ce("",!0)]),_:1}),t(C,{label:"备注",align:"center",prop:"remark","show-overflow-tooltip":!0}),t(C,{label:"状态",align:"center",prop:"status"},{default:l(e=>[t(re,{options:a(S),value:e.row.status},null,8,["options","value"])]),_:1}),t(C,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:l(e=>[L("span",null,O(i.parseTime(e.row.createTime)),1)]),_:1}),t(C,{label:"操作",width:"180",align:"center","class-name":"small-padding fixed-width"},{default:l(e=>[_((s(),m(c,{link:"",type:"primary",icon:"Edit",onClick:me=>j(e.row)},{default:l(()=>[u("修改")]),_:2},1032,["onClick"])),[[b,["system:advertConfig:edit"]]]),_((s(),m(c,{link:"",type:"primary",icon:"Delete",onClick:me=>B(e.row)},{default:l(()=>[u("删除")]),_:2},1032,["onClick"])),[[b,["system:advertConfig:remove"]]])]),_:1})]),_:1},8,["data"])),[[ce,a(T)]]),t(J,{total:a(z),"current-page":a(d).pageNum,"onUpdate:currentPage":o[3]||(o[3]=e=>a(d).pageNum=e),"page-size":a(d).pageSize,"onUpdate:pageSize":o[4]||(o[4]=e=>a(d).pageSize=e),onPagination:h},null,8,["total","current-page","page-size"]),t(pe,{title:a(D),modelValue:a(v),"onUpdate:modelValue":o[9]||(o[9]=e=>K(v)?v.value=e:null),width:"500px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:l(()=>[L("div",Te,[t(c,{type:"primary",onClick:Z},{default:l(()=>[u("确 定")]),_:1}),t(c,{onClick:H},{default:l(()=>[u("取 消")]),_:1})])]),default:l(()=>[t(E,{ref:"advertConfigRef",model:a(r),rules:a(G),"label-width":"80px"},{default:l(()=>[t(y,{label:"广告标题",prop:"advertTitle"},{default:l(()=>[t(w,{modelValue:a(r).advertTitle,"onUpdate:modelValue":o[5]||(o[5]=e=>a(r).advertTitle=e),placeholder:"请输入广告标题"},null,8,["modelValue"])]),_:1}),t(y,{label:"图片地址",prop:"picUrl"},{default:l(()=>[t(se,{modelValue:a(r).picUrl,"onUpdate:modelValue":o[6]||(o[6]=e=>a(r).picUrl=e),limit:1,action:"/file/upload/path",data:{path:"advertise"},"file-size":5,"file-type":["png","jpg","jpeg","gif","webp"]},null,8,["modelValue"])]),_:1}),t(y,{label:"状态",prop:"status"},{default:l(()=>[t(ue,{modelValue:a(r).status,"onUpdate:modelValue":o[7]||(o[7]=e=>a(r).status=e)},{default:l(()=>[(s(!0),R(Q,null,I(a(S),e=>(s(),m(de,{key:e.value,value:parseInt(e.value)},{default:l(()=>[u(O(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"备注",prop:"remark"},{default:l(()=>[t(w,{modelValue:a(r).remark,"onUpdate:modelValue":o[8]||(o[8]=e=>a(r).remark=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Pe as default};
