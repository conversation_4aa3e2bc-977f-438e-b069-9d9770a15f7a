import{s as k,M as Se,g as De,r as p,A as Ie,R as Oe,p as Ce,d as m,O as X,c as w,o as u,P as V,e as t,Q as Z,k as l,f as n,l as F,J as I,K as O,i as c,q as b,N as ee,t as U,h as K,j as Ue}from"./index-B3e47hUR.js";function xe(g){return k({url:"/system/owner/whitelist/list",method:"get",params:g})}function qe(g){return k({url:"/system/owner/whitelist/"+g,method:"get"})}function $e(g){return k({url:"/system/owner/whitelist",method:"post",data:g})}function Ye(g){return k({url:"/system/owner/whitelist",method:"put",data:g})}function Pe(g){return k({url:"/system/owner/whitelist/"+g,method:"delete"})}function Re(){return k({url:"/system/platform/warehouse/optionSelect",method:"get"})}function We(){return k({url:"/system/platform/operator/optionSelect",method:"get"})}function le(g){return k({url:"/system/platform/warehouse/optionSelectByOperator",method:"get",params:{operatorId:g}})}const Ee={class:"app-container"},Me={key:0,class:"time-display"},Be={class:"dialog-footer"},Fe=Se({name:"Whitelist"}),Le=Object.assign(Fe,{setup(g){const{proxy:h}=De(),L=p([]),N=p([]),P=p([]),_=p(!1),R=p(!0),Y=p(!0),Q=p([]),z=p(!0),ae=p(!0),W=p(0),E=p(""),x=p(!1),T=p(!1),C=p(!1),j=p([{label:"地面",value:0,elTagType:"success"},{label:"地库和地面",value:1,elTagType:"primary"}]),te=p([{label:"集团车辆",value:"集团车辆"},{label:"特殊优惠",value:"特殊优惠"}]),ne=Ie({form:{},queryParams:{pageNum:1,pageSize:10,plateNo:null,name:null,phoneNumber:null,operatorId:null,warehouseId:null,parkType:null},rules:{plateNo:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],name:[{required:!0,message:"姓名不能为空",trigger:"blur"}],phoneNumber:[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],operatorId:[{required:!0,message:"运营商不能为空",trigger:"change"}],selectedOperatorId:[{required:!0,message:"运营商不能为空",trigger:"change"}],warehouseId:[{required:!0,message:"场库不能为空",trigger:"change"}],parkType:[{required:!0,message:"停放区域类型不能为空",trigger:"change"}],beginTime:[{required:!0,message:"开始时间不能为空",trigger:"blur"}],endTime:[{required:!0,message:"结束时间不能为空",trigger:"blur"}]}}),{queryParams:i,form:o,rules:oe}=Oe(ne);function S(){R.value=!0,xe(i.value).then(r=>{L.value=r.rows,W.value=r.total,R.value=!1})}function A(){We().then(r=>{P.value=r.data})}function re(){Re().then(r=>{N.value=r.data})}function ue(r){o.value.warehouseId=null,N.value=[],r&&le(r).then(a=>{N.value=a.data})}function ie(){_.value=!1,M()}function M(){const r=new Date,a=r.getFullYear()+"-"+String(r.getMonth()+1).padStart(2,"0")+"-"+String(r.getDate()).padStart(2,"0")+" 00:00:00",d=r.getFullYear()+"-"+String(r.getMonth()+1).padStart(2,"0")+"-"+String(r.getDate()).padStart(2,"0");o.value={id:null,plateNo:null,name:null,phoneNumber:null,selectedOperatorId:null,operatorId:null,warehouseId:null,parkType:null,whiteType:null,beginTime:a,beginTimeDisplay:d,endTime:null,remark:null},N.value=[],x.value=!1,T.value=!1,h.resetForm("whitelistRef")}function q(){i.value.pageNum=1,S()}function de(){h.resetForm("queryRef"),q()}function se(r){Q.value=r.map(a=>a.id),z.value=r.length!=1,ae.value=!r.length}function pe(){M(),A(),_.value=!0,E.value="添加白名单管理"}function J(r){M();const a=r.id||Q.value;qe(a).then(d=>{const s=d.data;o.value={...s},o.value.beginTime&&(o.value.beginTimeDisplay=o.value.beginTime.split(" ")[0],o.value.beginTime=o.value.beginTime),o.value.endTime&&(o.value.endTime=o.value.endTime.split(" ")[0]),o.value.selectedOperatorId=o.value.operatorId,o.value.operatorId&&le(o.value.operatorId).then(y=>{N.value=y.data}),_.value=!0,E.value="修改白名单管理",x.value=!0}).catch(d=>{console.error("加载数据失败:",d),h.$modal.msgError("加载数据失败")})}function me(){h.$refs.whitelistRef.validate(r=>{if(r){T.value=!0;const a={...o.value};delete a.beginTimeDisplay,delete a.selectedOperatorId,a.beginTime&&!a.beginTime.includes(" ")&&(a.beginTime=a.beginTime+" 00:00:00"),a.endTime&&!a.endTime.includes(" ")&&(a.endTime=a.endTime+" 23:59:59"),o.value.id!=null?Ye(a).then(d=>{h.$modal.msgSuccess("修改成功"),_.value=!1,S()}).catch(d=>{console.error("修改失败:",d)}).finally(()=>{T.value=!1}):$e(a).then(d=>{h.$modal.msgSuccess("新增成功"),_.value=!1,S()}).catch(d=>{console.error("新增失败:",d)}).finally(()=>{T.value=!1})}})}function ce(r){if(!r||!r.id){h.$modal.msgError("请选择要删除的记录");return}h.$modal.confirm('是否确认删除车牌号为"'+r.plateNo+'"的白名单记录？').then(function(){return C.value=!0,Pe(r.id)}).then(()=>{S(),h.$modal.msgSuccess("删除成功")}).catch(a=>{console.error("删除失败:",a)}).finally(()=>{C.value=!1})}function ge(){h.download("system/owner/whitelist/export",{...i.value},`whitelist_${new Date().getTime()}.xlsx`)}function fe(r){if(!o.value.beginTime)return!1;const a=new Date(o.value.beginTime);return r.getTime()<a.getTime()}function be(r){return r?r.length===8?"success":"primary":"info"}function he(r){return r?r.length===8?"#d4edda":"#cce7ff":"#909399"}return Ce(()=>{S(),A(),re()}),(r,a)=>{const d=m("el-input"),s=m("el-form-item"),y=m("el-option"),D=m("el-select"),v=m("el-button"),G=m("el-form"),B=m("el-col"),ye=m("right-toolbar"),ve=m("el-row"),f=m("el-table-column"),H=m("el-tag"),we=m("el-table"),_e=m("pagination"),Te=m("el-date-picker"),Ve=m("el-dialog"),$=X("hasPermi"),ke=X("loading");return u(),w("div",Ee,[V(t(G,{model:l(i),ref:"queryRef",inline:!0,"label-width":"68px"},{default:n(()=>[t(s,{label:"车牌号",prop:"plateNo"},{default:n(()=>[t(d,{modelValue:l(i).plateNo,"onUpdate:modelValue":a[0]||(a[0]=e=>l(i).plateNo=e),placeholder:"请输入车牌号",clearable:"",style:{width:"200px"},onKeyup:F(q,["enter"])},null,8,["modelValue"])]),_:1}),t(s,{label:"姓名",prop:"name"},{default:n(()=>[t(d,{modelValue:l(i).name,"onUpdate:modelValue":a[1]||(a[1]=e=>l(i).name=e),placeholder:"请输入姓名",clearable:"",style:{width:"200px"},onKeyup:F(q,["enter"])},null,8,["modelValue"])]),_:1}),t(s,{label:"手机号",prop:"phoneNumber"},{default:n(()=>[t(d,{modelValue:l(i).phoneNumber,"onUpdate:modelValue":a[2]||(a[2]=e=>l(i).phoneNumber=e),placeholder:"请输入手机号",clearable:"",style:{width:"200px"},onKeyup:F(q,["enter"])},null,8,["modelValue"])]),_:1}),t(s,{label:"运营商",prop:"operatorId"},{default:n(()=>[t(D,{modelValue:l(i).operatorId,"onUpdate:modelValue":a[3]||(a[3]=e=>l(i).operatorId=e),placeholder:"请选择运营商",clearable:"",style:{width:"200px"},onChange:r.handleQueryOperatorChange},{default:n(()=>[(u(!0),w(I,null,O(l(P),e=>(u(),c(y,{key:e.id,label:e.companyName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),t(s,{label:"场库",prop:"warehouseId"},{default:n(()=>[t(D,{modelValue:l(i).warehouseId,"onUpdate:modelValue":a[4]||(a[4]=e=>l(i).warehouseId=e),placeholder:"请选择场库",clearable:"",style:{width:"200px"}},{default:n(()=>[(u(!0),w(I,null,O(l(N),e=>(u(),c(y,{key:e.id,label:e.warehouseName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(s,{label:"停放区域",prop:"parkType"},{default:n(()=>[t(D,{modelValue:l(i).parkType,"onUpdate:modelValue":a[5]||(a[5]=e=>l(i).parkType=e),placeholder:"请选择停放区域类型",clearable:"",style:{width:"200px"}},{default:n(()=>[(u(!0),w(I,null,O(l(j),e=>(u(),c(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(s,null,{default:n(()=>[t(v,{type:"primary",icon:"Search",onClick:q},{default:n(()=>[b("搜索")]),_:1}),t(v,{icon:"Refresh",onClick:de},{default:n(()=>[b("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Z,l(Y)]]),t(ve,{gutter:10,class:"mb8"},{default:n(()=>[t(B,{span:1.5},{default:n(()=>[V((u(),c(v,{type:"primary",plain:"",icon:"Plus",onClick:pe,disabled:l(T)||l(C)},{default:n(()=>[b("新增")]),_:1},8,["disabled"])),[[$,["owner:whitelist:add"]]])]),_:1}),t(B,{span:1.5},{default:n(()=>[V((u(),c(v,{type:"success",plain:"",icon:"Edit",disabled:l(z)||l(T)||l(C),onClick:J},{default:n(()=>[b("修改")]),_:1},8,["disabled"])),[[$,["owner:whitelist:edit"]]])]),_:1}),t(B,{span:1.5},{default:n(()=>[V((u(),c(v,{type:"warning",plain:"",icon:"Download",onClick:ge},{default:n(()=>[b("导出")]),_:1})),[[$,["owner:whitelist:export"]]])]),_:1}),t(ye,{showSearch:l(Y),"onUpdate:showSearch":a[6]||(a[6]=e=>ee(Y)?Y.value=e:null),onQueryTable:S},null,8,["showSearch"])]),_:1}),V((u(),c(we,{data:l(L),onSelectionChange:se},{default:n(()=>[t(f,{type:"selection",width:"55",align:"center"}),t(f,{label:"运营商",align:"center",prop:"operatorName"}),t(f,{label:"场库名称",align:"center",prop:"warehouseName"}),t(f,{label:"车牌号",align:"center",prop:"plateNo"},{default:n(e=>[t(H,{type:be(e.row.plateNo),color:he(e.row.plateNo),effect:"plain"},{default:n(()=>[b(U(e.row.plateNo),1)]),_:2},1032,["type","color"])]),_:1}),t(f,{label:"姓名",align:"center",prop:"name"}),t(f,{label:"手机号",align:"center",prop:"phoneNumber"}),t(f,{label:"停放区域类型",align:"center",prop:"parkType"},{default:n(e=>[t(H,{type:e.row.parkType===0||e.row.parkType==="0"?"success":"primary",size:"small"},{default:n(()=>[b(U(e.row.parkType===0||e.row.parkType==="0"?"地面":"地库和地面"),1)]),_:2},1032,["type"])]),_:1}),t(f,{label:"白名单类型",align:"center",prop:"whiteType"}),t(f,{label:"开始时间",align:"center",prop:"beginTime",width:"180"},{default:n(e=>[K("span",null,U(r.parseTime(e.row.beginTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1}),t(f,{label:"结束时间",align:"center",prop:"endTime",width:"180"},{default:n(e=>[K("span",null,U(r.parseTime(e.row.endTime,"{y}-{m}-{d}"))+" 23:59:59",1)]),_:1}),t(f,{label:"备注",align:"center",prop:"remark"}),t(f,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(e=>[V((u(),c(v,{link:"",type:"primary",icon:"Edit",onClick:Ne=>J(e.row),disabled:l(C)},{default:n(()=>[b("修改")]),_:2},1032,["onClick","disabled"])),[[$,["owner:whitelist:edit"]]]),V((u(),c(v,{link:"",type:"primary",icon:"Delete",onClick:Ne=>ce(e.row),loading:l(C)},{default:n(()=>[b("删除")]),_:2},1032,["onClick","loading"])),[[$,["owner:whitelist:remove"]]])]),_:1})]),_:1},8,["data"])),[[ke,l(R)]]),V(t(_e,{total:l(W),page:l(i).pageNum,"onUpdate:page":a[7]||(a[7]=e=>l(i).pageNum=e),limit:l(i).pageSize,"onUpdate:limit":a[8]||(a[8]=e=>l(i).pageSize=e),onPagination:S},null,8,["total","page","limit"]),[[Z,l(W)>0]]),t(Ve,{title:l(E),modelValue:l(_),"onUpdate:modelValue":a[19]||(a[19]=e=>ee(_)?_.value=e:null),width:"600px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:n(()=>[K("div",Be,[t(v,{type:"primary",onClick:me,loading:l(T)},{default:n(()=>[b("确 定")]),_:1},8,["loading"]),t(v,{onClick:ie,disabled:l(T)},{default:n(()=>[b("取 消")]),_:1},8,["disabled"])])]),default:n(()=>[t(G,{ref:"whitelistRef",model:l(o),rules:l(oe),"label-width":"100px"},{default:n(()=>[t(s,{label:"车牌号",prop:"plateNo"},{default:n(()=>[t(d,{modelValue:l(o).plateNo,"onUpdate:modelValue":a[9]||(a[9]=e=>l(o).plateNo=e),placeholder:"请输入车牌号",disabled:l(x)},null,8,["modelValue","disabled"])]),_:1}),t(s,{label:"姓名",prop:"name"},{default:n(()=>[t(d,{modelValue:l(o).name,"onUpdate:modelValue":a[10]||(a[10]=e=>l(o).name=e),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),t(s,{label:"手机号",prop:"phoneNumber"},{default:n(()=>[t(d,{modelValue:l(o).phoneNumber,"onUpdate:modelValue":a[11]||(a[11]=e=>l(o).phoneNumber=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),t(s,{label:"运营商",prop:"selectedOperatorId"},{default:n(()=>[t(D,{modelValue:l(o).selectedOperatorId,"onUpdate:modelValue":a[12]||(a[12]=e=>l(o).selectedOperatorId=e),placeholder:"请选择运营商",clearable:"",style:{width:"100%"},onChange:ue,disabled:l(x)},{default:n(()=>[(u(!0),w(I,null,O(l(P),e=>(u(),c(y,{key:e.id,label:e.companyName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),t(s,{label:"场库",prop:"warehouseId"},{default:n(()=>[t(D,{modelValue:l(o).warehouseId,"onUpdate:modelValue":a[13]||(a[13]=e=>l(o).warehouseId=e),placeholder:"请选择场库",clearable:"",style:{width:"100%"},disabled:!l(o).selectedOperatorId||l(x)},{default:n(()=>[(u(!0),w(I,null,O(l(N),e=>(u(),c(y,{key:e.id,label:e.warehouseName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),t(s,{label:"停放类型",prop:"parkType"},{default:n(()=>[t(D,{modelValue:l(o).parkType,"onUpdate:modelValue":a[14]||(a[14]=e=>l(o).parkType=e),placeholder:"请选择停放区域类型",clearable:"",style:{width:"100%"}},{default:n(()=>[(u(!0),w(I,null,O(l(j),e=>(u(),c(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(s,{label:"白名单类型",prop:"whiteType"},{default:n(()=>[t(D,{modelValue:l(o).whiteType,"onUpdate:modelValue":a[15]||(a[15]=e=>l(o).whiteType=e),placeholder:"请选择白名单类型",clearable:"",style:{width:"100%"}},{default:n(()=>[(u(!0),w(I,null,O(l(te),e=>(u(),c(y,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(s,{label:"开始时间",prop:"beginTime"},{default:n(()=>[t(d,{modelValue:l(o).beginTimeDisplay,"onUpdate:modelValue":a[16]||(a[16]=e=>l(o).beginTimeDisplay=e),placeholder:"开始时间",disabled:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(s,{label:"结束时间",prop:"endTime"},{default:n(()=>[t(Te,{modelValue:l(o).endTime,"onUpdate:modelValue":a[17]||(a[17]=e=>l(o).endTime=e),type:"date",placeholder:"选择结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":fe,style:{width:"100%"}},null,8,["modelValue"]),l(o).beginTimeDisplay&&l(o).endTime?(u(),w("div",Me," 时间："+U(l(o).beginTimeDisplay)+" 00:00:00 - "+U(l(o).endTime)+" 23:59:59 ",1)):Ue("",!0)]),_:1}),t(s,{label:"备注",prop:"remark"},{default:n(()=>[t(d,{modelValue:l(o).remark,"onUpdate:modelValue":a[18]||(a[18]=e=>l(o).remark=e),type:"textarea",placeholder:"请输入内容"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Le as default};
