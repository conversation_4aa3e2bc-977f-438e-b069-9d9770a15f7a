import{s as S,_ as ne,M as oe,g as re,r as i,A as se,R as ue,d as s,O as L,c as C,o as p,P as f,e,Q as ie,k as a,f as t,l as T,J as pe,K as de,i as m,q as _,N as O,h as Q,t as ce}from"./index-B3e47hUR.js";import{C as me}from"./index-DFSldapw.js";function _e(g){return S({url:"/system/owner/wxuser/list",method:"get",params:g})}function ge(g){return S({url:"/system/owner/wxuser/"+g,method:"delete"})}function fe(g){return S({url:"/system/owner/wxuser/cars/"+g,method:"get"})}const we={class:"app-container"},be={key:1},ve={class:"dialog-footer"},ye=oe({name:"WxUser"}),he=Object.assign(ye,{setup(g){const{proxy:w}=re(),{wx_user_status:xe,wx_user_type:U}=w.useDict("wx_user_status","wx_user_type"),W=i([{label:"燃油",value:"1"},{label:"纯电",value:"2"},{label:"混动",value:"3"}]),j=i([{label:"否",value:"0"},{label:"是",value:"1"}]),D=i([]),P=i([]),b=i(!1),k=i(!0),x=i(!0),z=i([]),E=i(!0),$=i(!0),R=i(0),F=se({queryParams:{pageNum:1,pageSize:10,userName:null,nickName:null,phoneNumber:null,status:null,userType:null}}),{queryParams:o}=ue(F);function v(){k.value=!0,_e(o.value).then(r=>{D.value=r.rows,R.value=r.total,k.value=!1})}function y(){o.value.pageNum=1,v()}function I(){w.resetForm("queryRef"),Object.assign(o.value,{pageNum:1,pageSize:10,userName:null,nickName:null,phoneNumber:null,status:null,userType:null}),y()}function M(r){z.value=r.map(n=>n.id),E.value=r.length!=1,$.value=!r.length}function q(r){const n=r.id||z.value;let d="";r&&r.phoneNumber?d=`确认删除手机号为"${r.phoneNumber}"的用户吗？`:d="确认删除选中的用户吗？",w.$modal.confirm(d).then(function(){return ge(n)}).then(()=>{v(),w.$modal.msgSuccess("删除成功")}).catch(()=>{})}function A(){w.download("owner/wxuser/export",{...o.value},`wxuser_${new Date().getTime()}.xlsx`)}function J(r){fe(r.id).then(n=>{P.value=n.data,b.value=!0})}return v(),(r,n)=>{const d=s("el-input"),h=s("el-form-item"),G=s("el-option"),H=s("el-select"),c=s("el-button"),X=s("el-form"),B=s("el-col"),Y=s("right-toolbar"),Z=s("el-row"),u=s("el-table-column"),ee=s("el-image"),V=s("dict-tag"),K=s("el-table"),le=s("el-dialog"),N=L("hasPermi"),te=L("loading");return p(),C("div",we,[f(e(X,{model:a(o),ref:"queryRef",inline:!0,"label-width":"68px"},{default:t(()=>[e(h,{label:"账号",prop:"userName"},{default:t(()=>[e(d,{modelValue:a(o).userName,"onUpdate:modelValue":n[0]||(n[0]=l=>a(o).userName=l),placeholder:"请输入账号",clearable:"",style:{width:"200px"},onKeyup:T(y,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"姓名",prop:"nickName"},{default:t(()=>[e(d,{modelValue:a(o).nickName,"onUpdate:modelValue":n[1]||(n[1]=l=>a(o).nickName=l),placeholder:"请输入姓名",clearable:"",style:{width:"200px"},onKeyup:T(y,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"手机号",prop:"phoneNumber"},{default:t(()=>[e(d,{modelValue:a(o).phoneNumber,"onUpdate:modelValue":n[2]||(n[2]=l=>a(o).phoneNumber=l),placeholder:"请输入手机号",clearable:"",style:{width:"200px"},onKeyup:T(y,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"用户类型",prop:"userType"},{default:t(()=>[e(H,{modelValue:a(o).userType,"onUpdate:modelValue":n[3]||(n[3]=l=>a(o).userType=l),placeholder:"用户类型",clearable:"",style:{width:"200px"}},{default:t(()=>[(p(!0),C(pe,null,de(a(U),l=>(p(),m(G,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(h,null,{default:t(()=>[e(c,{type:"primary",icon:"Search",onClick:y},{default:t(()=>[_("搜索")]),_:1}),e(c,{icon:"Refresh",onClick:I},{default:t(()=>[_("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ie,a(x)]]),e(Z,{gutter:10,class:"mb8"},{default:t(()=>[e(B,{span:1.5},{default:t(()=>[f((p(),m(c,{type:"danger",plain:"",icon:"Delete",disabled:a($),onClick:q},{default:t(()=>[_("删除")]),_:1},8,["disabled"])),[[N,["owner:wxuser:remove"]]])]),_:1}),e(B,{span:1.5},{default:t(()=>[f((p(),m(c,{type:"warning",plain:"",icon:"Download",onClick:A},{default:t(()=>[_("导出")]),_:1})),[[N,["owner:wxuser:export"]]])]),_:1}),e(Y,{showSearch:a(x),"onUpdate:showSearch":n[4]||(n[4]=l=>O(x)?x.value=l:null),onQueryTable:v},null,8,["showSearch"])]),_:1}),f((p(),m(K,{data:a(D),onSelectionChange:M},{default:t(()=>[e(u,{type:"selection",width:"55",align:"center"}),e(u,{label:"头像",align:"center",prop:"img"},{default:t(l=>[l.row.img?(p(),m(ee,{key:0,style:{width:"40px",height:"40px","border-radius":"50%"},src:l.row.img,"preview-src-list":[l.row.img],fit:"cover"},null,8,["src","preview-src-list"])):(p(),C("span",be,"-"))]),_:1}),e(u,{label:"姓名",align:"center",prop:"nickName"}),e(u,{label:"账号",align:"center",prop:"userName"}),e(u,{label:"手机号",align:"center",prop:"phoneNumber"}),e(u,{label:"用户类型",align:"center",prop:"userType"},{default:t(l=>[e(V,{options:a(U),value:l.row.userType},null,8,["options","value"])]),_:1}),e(u,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(l=>[f((p(),m(c,{link:"",type:"primary",icon:"View",onClick:ae=>J(l.row)},{default:t(()=>[_("查询车辆")]),_:2},1032,["onClick"])),[[N,["owner:wxuser:car"]]]),f((p(),m(c,{link:"",type:"danger",icon:"Delete",onClick:ae=>q(l.row)},{default:t(()=>[_("删除")]),_:2},1032,["onClick"])),[[N,["owner:wxuser:remove"]]])]),_:1})]),_:1},8,["data"])),[[te,a(k)]]),e(me,{total:a(R),"current-page":a(o).pageNum,"onUpdate:currentPage":n[5]||(n[5]=l=>a(o).pageNum=l),"page-size":a(o).pageSize,"onUpdate:pageSize":n[6]||(n[6]=l=>a(o).pageSize=l),onPagination:v},null,8,["total","current-page","page-size"]),e(le,{title:"用户车辆信息",modelValue:a(b),"onUpdate:modelValue":n[8]||(n[8]=l=>O(b)?b.value=l:null),width:"800px","append-to-body":""},{footer:t(()=>[Q("div",ve,[e(c,{onClick:n[7]||(n[7]=l=>b.value=!1)},{default:t(()=>[_("关 闭")]),_:1})])]),default:t(()=>[e(K,{data:a(P),style:{width:"100%"}},{default:t(()=>[e(u,{label:"车牌号",align:"center",prop:"plateNo"}),e(u,{label:"品牌",align:"center",prop:"carBrand"}),e(u,{label:"车型",align:"center",prop:"carType"}),e(u,{label:"能源类型",align:"center",prop:"energyType"},{default:t(l=>[e(V,{options:a(W),value:l.row.energyType},null,8,["options","value"])]),_:1}),e(u,{label:"是否默认",align:"center",prop:"isDefault"},{default:t(l=>[e(V,{options:a(j),value:l.row.isDefault},null,8,["options","value"])]),_:1}),e(u,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:t(l=>[Q("span",null,ce(r.parseTime(l.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])])}}}),Ve=ne(he,[["__scopeId","data-v-68ce7d49"]]);export{Ve as default};
