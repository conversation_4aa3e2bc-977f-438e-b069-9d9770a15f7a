<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e031f56e-15de-4edb-bb03-c6ddf7f662bb" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="KEEP" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\soft\apache-maven-3.9.8-bin\apache-maven-3.9.8" />
        <option name="localRepository" value="E:\soft\apache-maven-3.9.8-bin\apache-maven-3.9.8\mvn_resp" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\soft\apache-maven-3.9.8-bin\apache-maven-3.9.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2y76KS74QnjmSDj372HXc2zSxxt" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Docker.lgjy-file/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-gate/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-gateway/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-system/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-wx-auth/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.lgjy-wx/Dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.lgjy-api [clean].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.LgjyAuthApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyFileApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyGateApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyGatewayApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyGenApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjySystemApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyWxApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.LgjyWxAuthApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.PaymentApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;park&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/wechat/success/wenjuan/wenjuan/tduck-platform/tduck-platform&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;configurable.group.build&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Docker.lgjy-gateway/Dockerfile">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="park-api" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="park-api" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.auth.LgjyAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyFileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="lgjy-modules-file" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.file.LgjyFileApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.lgjy.file.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyGateApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-gate" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.gate.LgjyGateApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.gateway.LgjyGatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjySystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="lgjy-modules-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.system.LgjySystemApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.lgjy.system.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyWxApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-modules-wx" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.wx.LgjyWxApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LgjyWxAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lgjy-wx-auth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.lgjy.wxauth.LgjyWxAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-image" temporary="true">
      <deployment type="docker-image">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-file/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-file:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="sourceFilePath" value="lgjy-modules/lgjy-file/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-gate/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-gate:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="sourceFilePath" value="lgjy-modules/lgjy-gate/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-gateway/Dockerfile" type="docker-deploy" factoryName="dockerfile" temporary="true" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-gateway:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="containerName" value="" />
          <option name="sourceFilePath" value="lgjy-gateway/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-system/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-system:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="sourceFilePath" value="lgjy-modules/lgjy-system/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-wx-auth/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-wx-auth:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="sourceFilePath" value="lgjy-wx-auth/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="lgjy-wx/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="park/lgjy-wx:3.6.9" />
          <option name="buildOnly" value="true" />
          <option name="sourceFilePath" value="lgjy-modules/lgjy-wx/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Docker.lgjy-file/Dockerfile" />
      <item itemvalue="Docker.lgjy-gate/Dockerfile" />
      <item itemvalue="Docker.lgjy-system/Dockerfile" />
      <item itemvalue="Docker.lgjy-wx/Dockerfile" />
      <item itemvalue="Docker.lgjy-wx-auth/Dockerfile" />
      <item itemvalue="Docker.lgjy-gateway/Dockerfile" />
      <item itemvalue="Spring Boot.LgjyAuthApplication" />
      <item itemvalue="Spring Boot.LgjyGateApplication" />
      <item itemvalue="Spring Boot.LgjyGatewayApplication" />
      <item itemvalue="Spring Boot.LgjyWxApplication" />
      <item itemvalue="Spring Boot.LgjyWxAuthApplication" />
      <item itemvalue="Spring Boot.LgjyFileApplication" />
      <item itemvalue="Spring Boot.LgjySystemApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.LgjySystemApplication" />
        <item itemvalue="Spring Boot.LgjyFileApplication" />
        <item itemvalue="Docker.lgjy-gateway/Dockerfile" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e031f56e-15de-4edb-bb03-c6ddf7f662bb" name="更改" comment="" />
      <created>1749173200359</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749173200359</updated>
      <workItem from="1749173201691" duration="3146000" />
      <workItem from="1749177796998" duration="9929000" />
      <workItem from="1749433443383" duration="5777000" />
      <workItem from="1749445476789" duration="7962000" />
      <workItem from="1749461755913" duration="2469000" />
      <workItem from="1749521110962" duration="7321000" />
      <workItem from="1749553028838" duration="6680000" />
      <workItem from="1749561031204" duration="2440000" />
      <workItem from="1749594658830" duration="3604000" />
      <workItem from="1749603728742" duration="10306000" />
      <workItem from="1749636265291" duration="2523000" />
      <workItem from="1749642533040" duration="14000" />
      <workItem from="1749642678406" duration="1592000" />
      <workItem from="1749664782336" duration="283000" />
      <workItem from="1749666190614" duration="7864000" />
      <workItem from="1749692530103" duration="14943000" />
      <workItem from="1749721103515" duration="6544000" />
      <workItem from="1749758719102" duration="984000" />
      <workItem from="1749783661596" duration="3982000" />
      <workItem from="1749861106002" duration="33000" />
      <workItem from="1750053797912" duration="4863000" />
      <workItem from="1750070052649" duration="2931000" />
      <workItem from="1750123126256" duration="7795000" />
      <workItem from="1750167322133" duration="1000" />
      <workItem from="1750209390872" duration="15328000" />
      <workItem from="1750286343731" duration="17000" />
      <workItem from="1750305497952" duration="10884000" />
      <workItem from="1750327857004" duration="36000" />
      <workItem from="1750388801077" duration="11263000" />
      <workItem from="1750407873835" duration="7000" />
      <workItem from="1750415831415" duration="2941000" />
      <workItem from="1750487573598" duration="3159000" />
      <workItem from="1750729726316" duration="10898000" />
      <workItem from="1750826389471" duration="6219000" />
      <workItem from="1750900130881" duration="6902000" />
      <workItem from="1750915701107" duration="122000" />
      <workItem from="1750915849306" duration="286000" />
      <workItem from="1750916351973" duration="4459000" />
      <workItem from="1750935787318" duration="708000" />
      <workItem from="1750989498308" duration="4508000" />
      <workItem from="1751236438313" duration="29000" />
      <workItem from="1751258185150" duration="8777000" />
      <workItem from="1751332561386" duration="13394000" />
      <workItem from="1751364016005" duration="2465000" />
      <workItem from="1751379420591" duration="13000" />
      <workItem from="1751387561235" duration="145000" />
      <workItem from="1751419071502" duration="9394000" />
      <workItem from="1751450564035" duration="12879000" />
      <workItem from="1751501228919" duration="1979000" />
      <workItem from="1751506368710" duration="31000" />
      <workItem from="1751508662342" duration="9182000" />
      <workItem from="1751534540625" duration="8000" />
      <workItem from="1751591668591" duration="9263000" />
      <workItem from="1751708173811" duration="5760000" />
      <workItem from="1751761067943" duration="7000" />
      <workItem from="1751956634811" duration="4016000" />
      <workItem from="1751969676990" duration="7778000" />
      <workItem from="1752023997671" duration="7289000" />
      <workItem from="1752064302385" duration="1811000" />
      <workItem from="1752111857788" duration="15340000" />
      <workItem from="1752197163229" duration="19364000" />
      <workItem from="1752373188893" duration="2487000" />
      <workItem from="1752385119121" duration="4821000" />
      <workItem from="1752589394605" duration="2160000" />
      <workItem from="1752597202103" duration="3903000" />
      <workItem from="1752631136002" duration="13481000" />
      <workItem from="1752679093737" duration="14000" />
      <workItem from="1752720710087" duration="3055000" />
      <workItem from="1752748658589" duration="16000" />
      <workItem from="1752975179498" duration="9190000" />
      <workItem from="1753054749852" duration="13000" />
      <workItem from="1753155891622" duration="1922000" />
      <workItem from="1753546804989" duration="896000" />
      <workItem from="1753590220605" duration="8799000" />
      <workItem from="1753619878049" duration="18000" />
      <workItem from="1753665324566" duration="7328000" />
      <workItem from="1753677469899" duration="8230000" />
      <workItem from="1753763128455" duration="1432000" />
      <workItem from="1753838263169" duration="1796000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>