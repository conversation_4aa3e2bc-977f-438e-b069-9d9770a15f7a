import{s as C,M as fe,g as _e,r as m,A as ye,R as ge,d as a,O as M,c as N,o as r,P as w,e as t,Q as ve,k as o,f as l,l as G,J as I,K as q,i as f,q as c,N as H,h as W,t as X}from"./index-B3e47hUR.js";import{C as Y}from"./index-DFSldapw.js";function be(d){return C({url:"/system/notice/list",method:"get",params:d})}function he(d){return C({url:"/system/notice/"+d,method:"get"})}function we(d){return C({url:"/system/notice",method:"post",data:d})}function Ve(d){return C({url:"/system/notice",method:"put",data:d})}function Te(d){return C({url:"/system/notice/"+d,method:"delete"})}const Ce={class:"app-container"},ke={class:"dialog-footer"},Se=fe({name:"Notice"}),xe=Object.assign(Se,{components:{CustomPagination:Y}},{setup(d){const{proxy:_}=_e(),{sys_notice_status:D,sys_notice_type:U}=_.useDict("sys_notice_status","sys_notice_type"),$=m([]),y=m(!1),x=m(!0),k=m(!0),B=m([]),z=m(!0),F=m(!0),K=m(0),P=m(""),Z=ye({form:{},queryParams:{pageNum:1,pageSize:10,noticeTitle:void 0,createBy:void 0,status:void 0},rules:{noticeTitle:[{required:!0,message:"公告标题不能为空",trigger:"blur"}],noticeType:[{required:!0,message:"公告类型不能为空",trigger:"change"}]}}),{queryParams:s,form:i,rules:ee}=ge(Z);function v(){x.value=!0,be(s.value).then(u=>{$.value=u.rows,K.value=u.total,x.value=!1})}function te(){y.value=!1,R()}function R(){i.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},_.resetForm("noticeRef")}function S(){s.value.pageNum=1,v()}function le(){_.resetForm("queryRef"),S()}function oe(u){B.value=u.map(n=>n.noticeId),z.value=u.length!=1,F.value=!u.length}function ne(){R(),y.value=!0,P.value="添加公告"}function Q(u){R();const n=u.noticeId||B.value;he(n).then(V=>{i.value=V.data,y.value=!0,P.value="修改公告"})}function ae(){_.$refs.noticeRef.validate(u=>{u&&(i.value.noticeId!=null?Ve(i.value).then(n=>{_.$modal.msgSuccess("修改成功"),y.value=!1,v()}):we(i.value).then(n=>{_.$modal.msgSuccess("新增成功"),y.value=!1,v()}))})}function E(u){const n=u.noticeId||B.value;_.$modal.confirm('是否确认删除公告编号为"'+n+'"的数据项？').then(function(){return Te(n)}).then(()=>{v(),_.$modal.msgSuccess("删除成功")}).catch(()=>{})}return v(),(u,n)=>{const V=a("el-input"),g=a("el-form-item"),L=a("el-option"),A=a("el-select"),p=a("el-button"),O=a("el-form"),b=a("el-col"),ie=a("right-toolbar"),j=a("el-row"),h=a("el-table-column"),J=a("dict-tag"),ue=a("el-table"),se=a("el-radio"),re=a("el-radio-group"),de=a("editor"),ce=a("el-dialog"),T=M("hasPermi"),pe=M("loading");return r(),N("div",Ce,[w(t(O,{model:o(s),ref:"queryRef",inline:!0},{default:l(()=>[t(g,{label:"公告标题",prop:"noticeTitle"},{default:l(()=>[t(V,{modelValue:o(s).noticeTitle,"onUpdate:modelValue":n[0]||(n[0]=e=>o(s).noticeTitle=e),placeholder:"请输入公告标题",clearable:"",style:{width:"200px"},onKeyup:G(S,["enter"])},null,8,["modelValue"])]),_:1}),t(g,{label:"操作人员",prop:"createBy"},{default:l(()=>[t(V,{modelValue:o(s).createBy,"onUpdate:modelValue":n[1]||(n[1]=e=>o(s).createBy=e),placeholder:"请输入操作人员",clearable:"",style:{width:"200px"},onKeyup:G(S,["enter"])},null,8,["modelValue"])]),_:1}),t(g,{label:"类型",prop:"noticeType"},{default:l(()=>[t(A,{modelValue:o(s).noticeType,"onUpdate:modelValue":n[2]||(n[2]=e=>o(s).noticeType=e),placeholder:"公告类型",clearable:"",style:{width:"200px"}},{default:l(()=>[(r(!0),N(I,null,q(o(U),e=>(r(),f(L,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(g,null,{default:l(()=>[t(p,{type:"primary",icon:"Search",onClick:S},{default:l(()=>[c("搜索")]),_:1}),t(p,{icon:"Refresh",onClick:le},{default:l(()=>[c("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ve,o(k)]]),t(j,{gutter:10,class:"mb8"},{default:l(()=>[t(b,{span:1.5},{default:l(()=>[w((r(),f(p,{type:"primary",plain:"",icon:"Plus",onClick:ne},{default:l(()=>[c("新增")]),_:1})),[[T,["system:notice:add"]]])]),_:1}),t(b,{span:1.5},{default:l(()=>[w((r(),f(p,{type:"success",plain:"",icon:"Edit",disabled:o(z),onClick:Q},{default:l(()=>[c("修改")]),_:1},8,["disabled"])),[[T,["system:notice:edit"]]])]),_:1}),t(b,{span:1.5},{default:l(()=>[w((r(),f(p,{type:"danger",plain:"",icon:"Delete",disabled:o(F),onClick:E},{default:l(()=>[c("删除")]),_:1},8,["disabled"])),[[T,["system:notice:remove"]]])]),_:1}),t(ie,{showSearch:o(k),"onUpdate:showSearch":n[3]||(n[3]=e=>H(k)?k.value=e:null),onQueryTable:v},null,8,["showSearch"])]),_:1}),w((r(),f(ue,{data:o($),onSelectionChange:oe},{default:l(()=>[t(h,{type:"selection",width:"55",align:"center"}),t(h,{label:"公告标题",align:"center",prop:"noticeTitle","show-overflow-tooltip":!0}),t(h,{label:"公告类型",align:"center",prop:"noticeType",width:"100"},{default:l(e=>[t(J,{options:o(U),value:e.row.noticeType},null,8,["options","value"])]),_:1}),t(h,{label:"状态",align:"center",prop:"status",width:"100"},{default:l(e=>[t(J,{options:o(D),value:e.row.status},null,8,["options","value"])]),_:1}),t(h,{label:"创建者",align:"center",prop:"createBy",width:"100"}),t(h,{label:"创建时间",align:"center",prop:"createTime",width:"100"},{default:l(e=>[W("span",null,X(u.parseTime(e.row.createTime,"{y}-{m}-{d}")),1)]),_:1}),t(h,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(e=>[w((r(),f(p,{link:"",type:"primary",icon:"Edit",onClick:me=>Q(e.row)},{default:l(()=>[c("修改")]),_:2},1032,["onClick"])),[[T,["system:notice:edit"]]]),w((r(),f(p,{link:"",type:"primary",icon:"Delete",onClick:me=>E(e.row)},{default:l(()=>[c("删除")]),_:2},1032,["onClick"])),[[T,["system:notice:remove"]]])]),_:1})]),_:1},8,["data"])),[[pe,o(x)]]),t(Y,{total:o(K),"current-page":o(s).pageNum,"onUpdate:currentPage":n[4]||(n[4]=e=>o(s).pageNum=e),"page-size":o(s).pageSize,"onUpdate:pageSize":n[5]||(n[5]=e=>o(s).pageSize=e),onPagination:v},null,8,["total","current-page","page-size"]),t(ce,{title:o(P),modelValue:o(y),"onUpdate:modelValue":n[10]||(n[10]=e=>H(y)?y.value=e:null),width:"780px","append-to-body":""},{footer:l(()=>[W("div",ke,[t(p,{type:"primary",onClick:ae},{default:l(()=>[c("确 定")]),_:1}),t(p,{onClick:te},{default:l(()=>[c("取 消")]),_:1})])]),default:l(()=>[t(O,{ref:"noticeRef",model:o(i),rules:o(ee),"label-width":"80px"},{default:l(()=>[t(j,null,{default:l(()=>[t(b,{span:12},{default:l(()=>[t(g,{label:"公告标题",prop:"noticeTitle"},{default:l(()=>[t(V,{modelValue:o(i).noticeTitle,"onUpdate:modelValue":n[6]||(n[6]=e=>o(i).noticeTitle=e),placeholder:"请输入公告标题"},null,8,["modelValue"])]),_:1})]),_:1}),t(b,{span:12},{default:l(()=>[t(g,{label:"公告类型",prop:"noticeType"},{default:l(()=>[t(A,{modelValue:o(i).noticeType,"onUpdate:modelValue":n[7]||(n[7]=e=>o(i).noticeType=e),placeholder:"请选择"},{default:l(()=>[(r(!0),N(I,null,q(o(U),e=>(r(),f(L,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:24},{default:l(()=>[t(g,{label:"状态"},{default:l(()=>[t(re,{modelValue:o(i).status,"onUpdate:modelValue":n[8]||(n[8]=e=>o(i).status=e)},{default:l(()=>[(r(!0),N(I,null,q(o(D),e=>(r(),f(se,{key:e.value,value:e.value},{default:l(()=>[c(X(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(b,{span:24},{default:l(()=>[t(g,{label:"内容"},{default:l(()=>[t(de,{modelValue:o(i).noticeContent,"onUpdate:modelValue":n[9]||(n[9]=e=>o(i).noticeContent=e),"min-height":192},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{xe as default};
