import{s as A,_ as Ae,M as Ce,g as Ue,r as g,A as Re,R as Ye,p as $e,d as c,O as de,c as r,o as u,P as I,e as l,Q as Me,f as a,l as qe,k as t,J as O,K as D,i as f,q as i,h as P,t as s,Z as U,j as se,y as ze,z as He}from"./index-B3e47hUR.js";import{o as Fe}from"./warehouse-I5QKWbAT.js";import{C as ie}from"./index-DFSldapw.js";function Le(y){return A({url:"/system/parkingOrder/list",method:"get",params:y})}function Be(y){return A({url:"/system/parkingOrder/"+y,method:"get"})}function Qe(y){return A({url:"/system/parkingOrder",method:"post",data:y})}function je(y){return A({url:"/system/parkingOrder",method:"put",data:y})}function Ke(y){return A({url:"/system/parkingOrder/"+y,method:"delete"})}function Ee(){return A({url:"/system/parkingOrder/carTypeOptions",method:"get"})}function We(y){return A({url:"/system/parkingOrder/refund",method:"post",data:y})}const Je=y=>(ze("data-v-1ede361d"),y=y(),He(),y),Ze={class:"app-container"},Ge={key:1},Xe={key:1},el={key:0},ll={key:1},al={key:0,class:"amount-text"},tl={key:1},nl={key:0,class:"discount-text"},ol={key:1},ul={key:0,class:"actual-payment-text"},rl={key:1},dl={key:0},sl={key:1},il={key:0},pl={key:1},ml={key:0},cl={key:1},fl={key:0,class:"order-detail-view"},yl={class:"card-header"},_l=Je(()=>P("span",{class:"header-title"},"订单详情",-1)),gl={key:1},hl={key:1},vl={key:0,class:"duration-text"},kl={key:1},bl={key:0,class:"amount-text"},wl={key:1},Vl={key:0,class:"discount-text"},Tl={key:1},Pl={key:0,class:"actual-payment-text"},Sl={key:1},xl={class:"dialog-footer"},Nl={class:"actual-payment-text"},Il={class:"dialog-footer"},Ol=Ce({name:"ParkingOrder"}),Dl=Object.assign(Ol,{components:{CustomPagination:ie}},{setup(y){const{proxy:k}=Ue(),{pay_status:M,pay_method:q}=k.useDict("pay_status","pay_method"),E=g([]),C=g(!1),R=g(!1),F=g(!0),L=g(!0),W=g([]),pe=g(!0),J=g(!0),Z=g(0),G=g(""),S=g([]),B=g([]),X=g([]),z=g(!1),me=Re({form:{},refundForm:{},queryParams:{pageNum:1,pageSize:10,plateNo:null,warehouseId:null,payStatus:null,payType:null,carType:null,beginTime:null,endTime:null},rules:{plateNo:[{required:!0,message:"车牌号不能为空",trigger:"blur"}],payStatus:[{required:!0,message:"支付状态不能为空",trigger:"change"}]},refundRules:{actualPayment:[{required:!0,message:"退款金额不能为空",trigger:"blur"},{type:"number",min:.01,message:"退款金额必须大于0",trigger:"blur"}]}}),{queryParams:m,form:o,rules:ce,refundForm:b,refundRules:fe}=Ye(me);function T(){F.value=!0,m.value.params={},S.value!=null&&S.value!=""&&(m.value.beginTime=S.value[0],m.value.endTime=S.value[1]),Le(m.value).then(d=>{E.value=d.rows,Z.value=d.total,F.value=!1})}function Q(){m.value.pageNum=1,T()}function ye(){S.value=[],k.resetForm("queryRef"),Object.assign(m.value,{pageNum:1,pageSize:10,plateNo:null,warehouseId:null,payStatus:null,payType:null,carType:null,beginTime:null,endTime:null}),Q()}function _e(d){W.value=d.map(n=>n.id),pe.value=d.length!=1,J.value=!d.length}function ge(d){le(),z.value=!0;const n=d.id;Be(n).then(h=>{o.value=h.data,C.value=!0,G.value="查看停车订单详情"})}function ee(d){const n=d.id||W.value;k.$modal.confirm('是否确认删除停车订单编号为"'+n+'"的数据项？').then(function(){return Ke(n)}).then(()=>{T(),k.$modal.msgSuccess("删除成功")}).catch(()=>{})}function he(){k.download("system/parkingOrder/export",{...m.value},`parking_order_${new Date().getTime()}.xlsx`)}function ve(){k.$refs.orderRef.validate(d=>{d&&(o.value.id!=null?je(o.value).then(n=>{k.$modal.msgSuccess("修改成功"),C.value=!1,T()}):Qe(o.value).then(n=>{k.$modal.msgSuccess("新增成功"),C.value=!1,T()}))})}function ke(){C.value=!1,le()}function le(){o.value={id:null,warehouseId:null,parkingManageId:null,userId:null,plateNo:null,beginParkingTime:null,endParkingTime:null,parkingDuration:null,paymentAmount:null,discountAmount:null,actualPayment:null,payType:null,parkingReservationId:null,invoiceId:null,tradeId:null,payStatus:0,paymentTime:null,openId:null,carType:null},k.resetForm("orderRef")}function ae(d){if(!d)return"0分钟";const n=Math.floor(d/60),h=d%60;return n>0?h>0?`${n}小时${h}分钟`:`${n}小时`:`${h}分钟`}function be(){Fe().then(d=>{B.value=d.data||[]})}function we(){Ee().then(d=>{X.value=d.data||[]})}function te(d){return d?d.length===8?"success":"primary":"info"}function ne(d){return d?d.length===8?"#d4edda":"#cce7ff":"#909399"}function Ve(d){b.value={id:d.id,tradeId:d.tradeId,originalAmount:d.actualPayment,actualPayment:d.actualPayment,refundReason:""},R.value=!0}function Te(){k.$refs.refundRef.validate(d=>{d&&k.$modal.confirm("确认要退款 ¥"+b.value.actualPayment+" 吗？").then(()=>{We(b.value).then(n=>{k.$modal.msgSuccess("退款处理成功"),R.value=!1,T()})})})}function Pe(){R.value=!1,b.value={}}return $e(()=>{T(),be(),we()}),(d,n)=>{const h=c("el-input"),p=c("el-form-item"),x=c("el-option"),N=c("el-select"),j=c("el-date-picker"),V=c("el-button"),K=c("el-form"),v=c("el-col"),Se=c("right-toolbar"),oe=c("el-row"),_=c("el-table-column"),Y=c("el-tag"),H=c("dict-tag"),xe=c("el-table"),w=c("el-descriptions-item"),Ne=c("el-descriptions"),Ie=c("el-card"),ue=c("el-dialog"),Oe=c("el-input-number"),$=de("hasPermi"),De=de("loading");return u(),r("div",Ze,[I(l(K,{model:t(m),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[l(p,{label:"车牌号",prop:"plateNo"},{default:a(()=>[l(h,{modelValue:t(m).plateNo,"onUpdate:modelValue":n[0]||(n[0]=e=>t(m).plateNo=e),placeholder:"请输入车牌号",clearable:"",style:{width:"180px"},onKeyup:qe(Q,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"场库名称",prop:"warehouseId"},{default:a(()=>[l(N,{modelValue:t(m).warehouseId,"onUpdate:modelValue":n[1]||(n[1]=e=>t(m).warehouseId=e),placeholder:"请选择场库",clearable:"",style:{width:"200px"}},{default:a(()=>[(u(!0),r(O,null,D(B.value,e=>(u(),f(x,{key:e.id,label:e.warehouseName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"支付状态",prop:"payStatus"},{default:a(()=>[l(N,{modelValue:t(m).payStatus,"onUpdate:modelValue":n[2]||(n[2]=e=>t(m).payStatus=e),placeholder:"请选择支付状态",clearable:"",style:{width:"200px"}},{default:a(()=>[(u(!0),r(O,null,D(t(M),e=>(u(),f(x,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"支付方式",prop:"payType"},{default:a(()=>[l(N,{modelValue:t(m).payType,"onUpdate:modelValue":n[3]||(n[3]=e=>t(m).payType=e),placeholder:"请选择支付方式",clearable:"",style:{width:"200px"}},{default:a(()=>[(u(!0),r(O,null,D(t(q),e=>(u(),f(x,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"停车时间"},{default:a(()=>[l(j,{modelValue:S.value,"onUpdate:modelValue":n[4]||(n[4]=e=>S.value=e),style:{width:"240px"},"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),l(p,null,{default:a(()=>[l(V,{type:"primary",icon:"Search",onClick:Q},{default:a(()=>[i("搜索")]),_:1}),l(V,{icon:"Refresh",onClick:ye},{default:a(()=>[i("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Me,L.value]]),l(oe,{gutter:10,class:"mb8"},{default:a(()=>[l(v,{span:1.5},{default:a(()=>[I((u(),f(V,{type:"danger",plain:"",icon:"Delete",disabled:J.value,onClick:ee},{default:a(()=>[i("删除")]),_:1},8,["disabled"])),[[$,["order:parkingOrder:remove"]]])]),_:1}),l(v,{span:1.5},{default:a(()=>[I((u(),f(V,{type:"warning",plain:"",icon:"Download",onClick:he},{default:a(()=>[i("导出")]),_:1})),[[$,["order:parkingOrder:export"]]])]),_:1}),l(Se,{showSearch:L.value,"onUpdate:showSearch":n[5]||(n[5]=e=>L.value=e),onQueryTable:T},null,8,["showSearch"])]),_:1}),I((u(),f(xe,{data:E.value,onSelectionChange:_e},{default:a(()=>[l(_,{type:"selection",width:"55",align:"center"}),l(_,{label:"订单号",align:"center",prop:"tradeId",width:"180"},{default:a(e=>[P("span",null,s(e.row.tradeId||"--"),1)]),_:1}),l(_,{label:"场库名称",align:"center",prop:"warehouseName",width:"100"},{default:a(e=>[P("span",null,s(e.row.warehouseName||"--"),1)]),_:1}),l(_,{label:"车牌号",align:"center",prop:"plateNo",width:"120"},{default:a(e=>[e.row.plateNo?(u(),f(Y,{key:0,type:te(e.row.plateNo),color:ne(e.row.plateNo),effect:"plain"},{default:a(()=>[i(s(e.row.plateNo),1)]),_:2},1032,["type","color"])):(u(),r("span",Ge,"--"))]),_:1}),l(_,{label:"车辆类型",align:"center",prop:"carType",width:"100"},{default:a(e=>[e.row.carType?(u(),f(Y,{key:0,type:"primary"},{default:a(()=>[i(s(e.row.carType),1)]),_:2},1024)):(u(),r("span",Xe,"--"))]),_:1}),l(_,{label:"停车时长",align:"center",width:"100"},{default:a(e=>[e.row.parkingDuration?(u(),r("span",el,s(ae(e.row.parkingDuration)),1)):(u(),r("span",ll,"--"))]),_:1}),l(_,{label:"应付金额",align:"center",prop:"paymentAmount",width:"100"},{default:a(e=>[e.row.paymentAmount!==null&&e.row.paymentAmount!==void 0?(u(),r("span",al,"¥"+s(e.row.paymentAmount),1)):(u(),r("span",tl,"--"))]),_:1}),l(_,{label:"优惠金额",align:"center",prop:"discountAmount",width:"100"},{default:a(e=>[e.row.discountAmount!==null&&e.row.discountAmount!==void 0?(u(),r("span",nl,"¥"+s(e.row.discountAmount),1)):(u(),r("span",ol,"--"))]),_:1}),l(_,{label:"实付金额",align:"center",prop:"actualPayment",width:"100"},{default:a(e=>[e.row.actualPayment!==null&&e.row.actualPayment!==void 0?(u(),r("span",ul,"¥"+s(e.row.actualPayment),1)):(u(),r("span",rl,"--"))]),_:1}),l(_,{label:"支付方式",align:"center",prop:"payType",width:"100"},{default:a(e=>[l(H,{options:t(q),value:e.row.payType},null,8,["options","value"])]),_:1}),l(_,{label:"支付状态",align:"center",prop:"payStatus",width:"100"},{default:a(e=>[l(H,{options:t(M),value:e.row.payStatus},null,8,["options","value"])]),_:1}),l(_,{label:"开始停车时间",align:"center",prop:"beginParkingTime",width:"160"},{default:a(e=>[e.row.beginParkingTime?(u(),r("span",dl,s(t(U)(e.row.beginParkingTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)):(u(),r("span",sl,"--"))]),_:1}),l(_,{label:"结束停车时间",align:"center",prop:"endParkingTime",width:"160"},{default:a(e=>[e.row.endParkingTime?(u(),r("span",il,s(t(U)(e.row.endParkingTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)):(u(),r("span",pl,"--"))]),_:1}),l(_,{label:"支付时间",align:"center",prop:"paymentTime",width:"160"},{default:a(e=>[e.row.paymentTime?(u(),r("span",ml,s(t(U)(e.row.paymentTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)):(u(),r("span",cl,"--"))]),_:1}),l(_,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"180"},{default:a(e=>[I((u(),f(V,{link:"",type:"primary",icon:"View",onClick:re=>ge(e.row)},{default:a(()=>[i("查看")]),_:2},1032,["onClick"])),[[$,["order:parkingOrder:query"]]]),e.row.payStatus===3||e.row.payStatus===5?I((u(),f(V,{key:0,link:"",type:"danger",icon:"RefreshLeft",onClick:re=>Ve(e.row)},{default:a(()=>[i("退款")]),_:2},1032,["onClick"])),[[$,["order:parkingOrder:refund"]]]):se("",!0),I((u(),f(V,{link:"",type:"primary",icon:"Delete",onClick:re=>ee(e.row)},{default:a(()=>[i("删除")]),_:2},1032,["onClick"])),[[$,["order:parkingOrder:remove"]]])]),_:1})]),_:1},8,["data"])),[[De,F.value]]),l(ie,{total:Z.value,"current-page":t(m).pageNum,"onUpdate:currentPage":n[6]||(n[6]=e=>t(m).pageNum=e),"page-size":t(m).pageSize,"onUpdate:pageSize":n[7]||(n[7]=e=>t(m).pageSize=e),onPagination:T},null,8,["total","current-page","page-size"]),l(ue,{title:G.value,modelValue:C.value,"onUpdate:modelValue":n[19]||(n[19]=e=>C.value=e),width:"900px","append-to-body":""},{footer:a(()=>[P("div",xl,[z.value?se("",!0):(u(),f(V,{key:0,type:"primary",onClick:ve},{default:a(()=>[i("确 定")]),_:1})),l(V,{onClick:ke},{default:a(()=>[i(s(z.value?"关 闭":"取 消"),1)]),_:1})])]),default:a(()=>[z.value?(u(),r("div",fl,[l(Ie,{class:"detail-card",shadow:"never"},{header:a(()=>[P("div",yl,[_l,l(Y,{type:"primary",size:"small"},{default:a(()=>[i("订单号："+s(t(o).tradeId||"--"),1)]),_:1})])]),default:a(()=>[l(Ne,{column:2,border:""},{default:a(()=>[l(w,{label:"车牌号"},{default:a(()=>[t(o).plateNo?(u(),f(Y,{key:0,type:te(t(o).plateNo),color:ne(t(o).plateNo),effect:"plain"},{default:a(()=>[i(s(t(o).plateNo),1)]),_:1},8,["type","color"])):(u(),r("span",gl,"--"))]),_:1}),l(w,{label:"场库名称"},{default:a(()=>[i(s(t(o).warehouseName||"--"),1)]),_:1}),l(w,{label:"车辆类型"},{default:a(()=>[t(o).carType?(u(),f(Y,{key:0,type:"primary"},{default:a(()=>[i(s(t(o).carType),1)]),_:1})):(u(),r("span",hl,"--"))]),_:1}),l(w,{label:"停车时长"},{default:a(()=>[t(o).parkingDuration?(u(),r("span",vl,s(ae(t(o).parkingDuration)),1)):(u(),r("span",kl,"--"))]),_:1}),l(w,{label:"应付金额"},{default:a(()=>[t(o).paymentAmount!==null&&t(o).paymentAmount!==void 0?(u(),r("span",bl,"¥"+s(t(o).paymentAmount),1)):(u(),r("span",wl,"--"))]),_:1}),l(w,{label:"优惠金额"},{default:a(()=>[t(o).discountAmount!==null&&t(o).discountAmount!==void 0?(u(),r("span",Vl,"¥"+s(t(o).discountAmount),1)):(u(),r("span",Tl,"--"))]),_:1}),l(w,{label:"实付金额"},{default:a(()=>[t(o).actualPayment!==null&&t(o).actualPayment!==void 0?(u(),r("span",Pl,"¥"+s(t(o).actualPayment),1)):(u(),r("span",Sl,"--"))]),_:1}),l(w,{label:"支付方式"},{default:a(()=>[l(H,{options:t(q),value:t(o).payType},null,8,["options","value"])]),_:1}),l(w,{label:"支付状态"},{default:a(()=>[l(H,{options:t(M),value:t(o).payStatus},null,8,["options","value"])]),_:1}),l(w,{label:"支付时间"},{default:a(()=>[i(s(t(o).paymentTime?t(U)(t(o).paymentTime,"{y}-{m}-{d} {h}:{i}:{s}"):"--"),1)]),_:1}),l(w,{label:"开始停车时间"},{default:a(()=>[i(s(t(o).beginParkingTime?t(U)(t(o).beginParkingTime,"{y}-{m}-{d} {h}:{i}:{s}"):"--"),1)]),_:1}),l(w,{label:"结束停车时间"},{default:a(()=>[i(s(t(o).endParkingTime?t(U)(t(o).endParkingTime,"{y}-{m}-{d} {h}:{i}:{s}"):"--"),1)]),_:1})]),_:1})]),_:1})])):(u(),f(K,{key:1,ref:"orderRef",model:t(o),rules:t(ce),"label-width":"120px"},{default:a(()=>[l(oe,null,{default:a(()=>[l(v,{span:12},{default:a(()=>[l(p,{label:"车牌号",prop:"plateNo"},{default:a(()=>[l(h,{modelValue:t(o).plateNo,"onUpdate:modelValue":n[8]||(n[8]=e=>t(o).plateNo=e),placeholder:"请输入车牌号"},null,8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(p,{label:"场库名称",prop:"warehouseId"},{default:a(()=>[l(N,{modelValue:t(o).warehouseId,"onUpdate:modelValue":n[9]||(n[9]=e=>t(o).warehouseId=e),placeholder:"请选择场库",style:{width:"100%"}},{default:a(()=>[(u(!0),r(O,null,D(B.value,e=>(u(),f(x,{key:e.id,label:e.warehouseName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(p,{label:"车辆类型",prop:"carType"},{default:a(()=>[l(N,{modelValue:t(o).carType,"onUpdate:modelValue":n[10]||(n[10]=e=>t(o).carType=e),placeholder:"请选择车辆类型",style:{width:"100%"}},{default:a(()=>[(u(!0),r(O,null,D(X.value,e=>(u(),f(x,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(p,{label:"支付状态",prop:"payStatus"},{default:a(()=>[l(N,{modelValue:t(o).payStatus,"onUpdate:modelValue":n[11]||(n[11]=e=>t(o).payStatus=e),placeholder:"请选择支付状态",style:{width:"100%"}},{default:a(()=>[(u(!0),r(O,null,D(t(M),e=>(u(),f(x,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(p,{label:"支付方式",prop:"payType"},{default:a(()=>[l(N,{modelValue:t(o).payType,"onUpdate:modelValue":n[12]||(n[12]=e=>t(o).payType=e),placeholder:"请选择支付方式",style:{width:"100%"}},{default:a(()=>[(u(!0),r(O,null,D(t(q),e=>(u(),f(x,{key:e.value,label:e.label,value:parseInt(e.value)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(p,{label:"应付金额",prop:"paymentAmount"},{default:a(()=>[l(h,{modelValue:t(o).paymentAmount,"onUpdate:modelValue":n[13]||(n[13]=e=>t(o).paymentAmount=e),placeholder:"请输入应付金额"},{prepend:a(()=>[i("¥")]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(p,{label:"优惠金额",prop:"discountAmount"},{default:a(()=>[l(h,{modelValue:t(o).discountAmount,"onUpdate:modelValue":n[14]||(n[14]=e=>t(o).discountAmount=e),placeholder:"请输入优惠金额"},{prepend:a(()=>[i("¥")]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(p,{label:"实付金额",prop:"actualPayment"},{default:a(()=>[l(h,{modelValue:t(o).actualPayment,"onUpdate:modelValue":n[15]||(n[15]=e=>t(o).actualPayment=e),placeholder:"请输入实付金额"},{prepend:a(()=>[i("¥")]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(p,{label:"开始停车时间",prop:"beginParkingTime"},{default:a(()=>[l(j,{modelValue:t(o).beginParkingTime,"onUpdate:modelValue":n[16]||(n[16]=e=>t(o).beginParkingTime=e),type:"datetime",placeholder:"请选择开始停车时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(p,{label:"结束停车时间",prop:"endParkingTime"},{default:a(()=>[l(j,{modelValue:t(o).endParkingTime,"onUpdate:modelValue":n[17]||(n[17]=e=>t(o).endParkingTime=e),type:"datetime",placeholder:"请选择结束停车时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),l(v,{span:12},{default:a(()=>[l(p,{label:"停车时长(分钟)",prop:"parkingDuration"},{default:a(()=>[l(h,{modelValue:t(o).parkingDuration,"onUpdate:modelValue":n[18]||(n[18]=e=>t(o).parkingDuration=e),placeholder:"请输入停车时长"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"]))]),_:1},8,["title","modelValue"]),l(ue,{title:"退款处理",modelValue:R.value,"onUpdate:modelValue":n[22]||(n[22]=e=>R.value=e),width:"400px","append-to-body":""},{footer:a(()=>[P("div",Il,[l(V,{type:"primary",onClick:Te},{default:a(()=>[i("确 定")]),_:1}),l(V,{onClick:Pe},{default:a(()=>[i("取 消")]),_:1})])]),default:a(()=>[l(K,{ref:"refundRef",model:t(b),rules:t(fe),"label-width":"100px"},{default:a(()=>[l(p,{label:"订单号"},{default:a(()=>[P("span",null,s(t(b).tradeId),1)]),_:1}),l(p,{label:"实付金额"},{default:a(()=>[P("span",Nl,"¥"+s(t(b).originalAmount),1)]),_:1}),l(p,{label:"退款金额",prop:"actualPayment"},{default:a(()=>[l(Oe,{modelValue:t(b).actualPayment,"onUpdate:modelValue":n[20]||(n[20]=e=>t(b).actualPayment=e),min:0,max:t(b).originalAmount,precision:2,placeholder:"请输入退款金额",style:{width:"100%"}},null,8,["modelValue","max"])]),_:1}),l(p,{label:"退款原因"},{default:a(()=>[l(h,{modelValue:t(b).refundReason,"onUpdate:modelValue":n[21]||(n[21]=e=>t(b).refundReason=e),type:"textarea",placeholder:"请输入退款原因（选填）",rows:3},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}}),Rl=Ae(Dl,[["__scopeId","data-v-1ede361d"]]);export{Rl as default};
